<?php

namespace App\Http\Controllers\Admin;

use App\Constants\AdminRoleConst;
use App\Http\Controllers\Controller;
use App\Http\Helpers\Response;
use App\Models\Admin\Admin;
use App\Models\Admin\AdminRole;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Notifications\Admin\NewAdminCredential;
use App\Notifications\Admin\SendEmailToAll;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Role;

class AdminCareController extends Controller
{
    public function __construct()
    {
        $this->middleware('can:show admins')->only('index');
        $this->middleware('can:create admin')->only('create', 'store');
        $this->middleware('can:update admin')->only('edit', 'update');
        $this->middleware('can:delete admin')->only('deleteAdmin');
    }

    public function index()
    {
        $page_title = __("All Admin");
        $admins = Admin::paginate(10);
        $roles = Role::where('status', 'active')->get();

        return view('admin.sections.admin-care.index', compact(
            'page_title',
            'admins',
            'roles',
        ));
    }

    public function emailAllAdmins()
    {
        $page_title = __("Email To Admin");
        return view('admin.sections.admin-care.email-to-admins', compact(
            'page_title',
        ));
    }

    public function sendEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject'       => 'required|string|max:200',
            'message'       => 'required|string|max:2000',
        ]);

        $validated = $validator->validate();

        $admins = collect(Admin::get());

        try {
            Notification::send($admins, new SendEmailToAll($validated));
        } catch (Exception $e) {
            return back()->with(['error' => [__("Opps! Failed to send mail. Please recheck your mail credentials or reconfigure mail")]]);
        }

        return back()->with(['success' => [__('Email send successfully!')]]);
    }

    public function create()
    {
        $page_title = __("Create Admin");

        $roles = Role::where('status', 'active')->get();

        return view('admin.sections.admin-care.create', compact(
            'page_title',
            'roles',
        ));
    }

    public function store(Request $request)
    {
        //return $request->all();

        $validator = Validator::make($request->all(), [
            'firstname'         => "required|string|max:60",
            'lastname'          => "required|string|max:60",
            'username'          => "required|string|unique:admins,username|alpha_dash|max:25",
            'email'             => "required|email|unique:admins,email",
            'password'          => "required|min:8",
            'phone'             => "required|string|max:20|unique:admins,phone",
            'image'             => "nullable|mimes:png,jpg,jpeg,webp,svg",
            'role'              => "required|integer|exists:roles,id|max:30",
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput()->with("modal", "admin-add");
        }

        $role = Role::find($request->role);

        if ($role->status == 'inactive') {
            return back()->with(['warning' => [__('Role is inactive!')]]);
        }

        $validated = $validator->validate();
        $validated['password']      = Hash::make($validated['password']);
        $validated['status']        = true;

        if ($request->hasFile("image")) {
            try {
                $image = get_files_from_fileholder($request, "image");
                $upload_file = upload_files_from_path_dynamic($image, "admin-profile");
                $validated['image'] = $upload_file;
            } catch (Exception $e) {
                Log::error($e);
                return back()->with(['error' => [__('Opps! Failed to upload image.')]]);
            }
        }

        $data = Arr::except($validated, ['role']);

        try {
            $admin = Admin::create($data);
            $role  = Role::find($validated['role']);
            $admin->syncRoles($role);
            $validated['password'] = $request->password;
            Notification::route('mail', $validated['email'])->notify(new NewAdminCredential($validated));
        } catch (Exception $e) {
            Log::error($e);
            return back()->with(['error' => [$e->getMessage()]]);
        }

        return redirect()->route('admin.admins.index')->with(['success' => [__('New admin created successfully!')]]);
    }

    public function edit(Admin $admin)
    {
        $page_title = __("Edit Admin");

        $roles = Role::where('status', 'active')->get();

        return view('admin.sections.admin-care.edit', compact(
            'admin',
            'page_title',
            'roles',
        ));
    }

    public function update(Request $request, Admin $admin)
    {
        $request->merge(['old_image' => $admin->image]);

        $validator = Validator::make($request->all(), [
            'edit_firstname' => "required|string|max:60",
            'edit_lastname'  => "required|string|max:60",
            'edit_username'  => ["required", "string", "alpha_dash", Rule::unique('admins', 'username')->ignore($admin->id)],
            'edit_email'     => ["required", "string", "email", Rule::unique('admins', 'email')->ignore($admin->id)],
            'edit_phone'     => ["nullable", "string", Rule::unique('admins', 'phone')->ignore($admin->id)],
            'edit_image'     => "nullable|mimes:png,jpg,jpeg,svg,webp",
            'edit_role'      => "required|integer|exists:roles,id|max:30",
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput()->with("modal", "admin-edit");
        }

        $role = Role::find($request->edit_role);

        if ($role->status == 'inactive') {
            return back()->with(['warning' => [__('Role is inactive!')]]);
        }

        $validated = $validator->validate();
        $validated = Arr::except($validated, ['target']);


        if ($request->hasFile("edit_image")) {
            $image = get_files_from_fileholder($request, "edit_image");
            $upload_file = upload_files_from_path_dynamic($image, "admin-profile", $admin->image);
            $validated['edit_image'] = $upload_file;
        }

        $validated = replace_array_key($validated, "edit_");
        $data = Arr::except($validated, ['role']);

        try {
            $admin->update($data);
            $role  = Role::find($validated['role']);
            $admin->syncRoles($role);
        } catch (Exception $e) {
            Log::error($e);
            return back()->with(['error' => [__('Something went wrong! Please try again')]]);
        }


        return back()->with(['success' => [__('Admin information updated successfully!')]]);
    }

    public function deleteAdmin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'target' => 'required|string|exists:admins,username',
        ]);

        $validated = $validator->validate();

        $admin = Admin::where("username", $validated['target'])->first();
        if ($admin->id == 1) {
            return back()->with(['warning' => [__("Can't delete system super admin")]]);
        }

        if ($admin->id == auth()->user()->id) {
            return back()->with(['warning' => [__("Can't delete this")]]);
        }

        try {
            $admin->delete();
            delete_file(get_files_path('admin-profile') . '/' . $admin->image);
        } catch (Exception $e) {
            return back()->with(['error' => [__('Something went wrong! Please try again')]]);
        }

        return back()->with(['success' => [__('Admin deleted successfully!')]]);
    }

    public function adminSearch(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'text'  => 'required|string',
        ]);

        if ($validator->fails()) {
            $error = ['error' => $validator->errors()];
            return Response::error($error, null, 400);
        }

        $validated = $validator->validate();
        $admins = Admin::notAuth()->search($validated['text'])->select("firstname", "lastname", "username", "email", "image", "status", "phone")->limit(10)->get();
        return view('admin.components.search.admin-search', compact(
            'admins',
        ));
    }

    public function statusUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'data_target'       => "required|string|max:100",
            'status'            => "required|boolean",
        ]);

        if ($validator->stopOnFirstFailure()->fails()) {
            $error = ['error' => $validator->errors()];
            return Response::error($error, null, 400);
        }

        $validated = $validator->safe()->all();
        $username = $validated['data_target'];

        $admin = Admin::where('username', $username)->first();
        if (!$admin) {
            $error = ['error' => ['Admin not found!']];
            return Response::error($error, null, 404);
        }

        try {
            $admin->update([
                'status' => !$admin->status,
            ]);
        } catch (Exception $e) {
            $error = ['error' => [__('Something went wrong! Please try again')]];
            return Response::error($error, null, 500);
        }

        $success = ['success' => [__('Admin Status Updated Successfully')]];
        return Response::success($success, null, 200);
    }
}
