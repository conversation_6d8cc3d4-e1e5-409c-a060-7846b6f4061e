<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin\PaymentGatewayCurrency;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DepositSettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('can:update deposit settings')->only('index', 'depositChargeUpdate');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $page_title = __("Add Money Settings");
        $data = PaymentGatewayCurrency::where('payment_gateway_id', 1)->first();

        return view('admin.sections.add-money-settings.index', compact(
            'page_title',
            'data'
        ));
    }

    /**
     * Update transaction charges
     * @param Request closer
     * @return back view
     */
    public function depositChargeUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'slug'                              => 'required|string',
            $request->slug . '_fixed_charge'      => 'required|numeric',
            $request->slug . '_percent_charge'    => 'required|numeric',
            $request->slug . '_min_limit'         => 'required|numeric',
            $request->slug . '_max_limit'         => 'required|numeric|gt:' . $request->slug . '_min_limit',
        ]);

        $data = $request->all();

        $validator->validate();

        $fees_total = $data['add_money_fixed_charge'] + ($data['add_money_percent_charge'] / 100 * $data['add_money_min_limit']);

        if ($fees_total > $data['add_money_min_limit']) {
            return back()->with(['error' => [__('Minimum limit should be greater than or equal to ') . $fees_total]]);
        }

        $gateway_settings = PaymentGatewayCurrency::where('payment_gateway_id', 1)->first();

        try {
            $gateway_settings->update([
                'min_limit' => $data['add_money_min_limit'],
                'max_limit' => $data['add_money_max_limit'],
                'fixed_charge' => $data['add_money_fixed_charge'],
                'percent_charge' => $data['add_money_percent_charge'],
            ]);
        } catch (Exception $e) {
            return back()->with(['error' => [__('Something went wrong! Please try again')]]);
        }

        return back()->with(['success' => [__('Charge Updated Successfully!')]]);
    }
}
