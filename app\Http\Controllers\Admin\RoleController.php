<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware('can:show roles')->only('index');
        $this->middleware('can:create role')->only('create', 'store');
        $this->middleware('can:update role')->only('edit', 'update');
        $this->middleware('can:delete role')->only('destroy');
    }

    public function index()
    {
        $page_title = __("Roles");
        $roles = Role::get();

        return view('admin.sections.roles.index', compact(
            'page_title',
            'roles',
        ));
    }

    public function create()
    {
        $permissions = Permission::all()->groupBy('group');

        return view('admin.sections.roles.create', compact('permissions'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name'          => ['required', 'string', 'min:1', 'unique:roles,name'],
            'permissions'   => ['required', 'array'],
            'permissions.*' => ['required', 'exists:permissions,id'],
        ]);

        DB::transaction(function () use ($validated) {
            $role = Role::create([
                'name'   => $validated['name'],
                'guard_name' => 'admin',
                'status' => 'active'
            ]);

            $permissions = Permission::whereIn('id', $validated['permissions'])->get();
            $role->syncPermissions($permissions);
        });

        return to_route('admin.roles.index')->with(['success' => [__('Created successfully!')]]);
    }

    public function edit(Role $role)
    {
        $permissions = Permission::all()->groupBy('group');

        return view('admin.sections.roles.edit', compact('permissions', 'role'));
    }

    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name'          => ['required', 'string', 'min:1', Rule::unique('roles', 'name')->ignore($role->id)],
            'permissions'   => ['required', 'array'],
            'permissions.*' => ['required', 'exists:permissions,id'],
            'status'        => ['nullable', 'boolean'],
        ]);

        $status = !empty($validated['status']) ? 'active' : 'inactive';

        if ($status == 'inactive' && $role->users()->count() > 0) {
            return back()->with(['error' => [__("Can't deactivate this role because it is currently assigned to some administrators.")]]);
        }

        DB::transaction(function () use ($role, $validated, $status) {
            $role->update([
                'name'   => $validated['name'],
                'status' => $status,
            ]);

            $permissions = Permission::whereIn('id', $validated['permissions'])->get();
            $role->syncPermissions($permissions);
        });

        return to_route('admin.roles.index')->with(['success' => [__('Updated successfully')]]);
    }

    public function destroy(Role $role)
    {
        if ($role->id == 1) {
            throw new BadRequestHttpException(__("Can't delete super admin role"));
        }

        if ($role->users()->count() > 0) {
            return back()->with(['error' => [__("Can't deactivate this role because it is currently assigned to some administrators.")]]);
        }

        $role->delete();

        return back()->with(['success' => [__('Deleted successfully')]]);
    }
}
