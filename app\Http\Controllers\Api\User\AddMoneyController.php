<?php

namespace App\Http\Controllers\Api\User;

use Exception;
use App\Models\Network;
use App\Models\UserWallet;
use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Models\TemporaryData;
use App\Models\Admin\Currency;
use Illuminate\Support\Carbon;
use App\Models\UserCryptoWallet;
use App\Models\UserNotification;
use App\Http\Helpers\Api\Helpers;
use Illuminate\Support\Facades\DB;
use App\Constants\NotificationConst;
use App\Http\Controllers\Controller;
use App\Models\Admin\PaymentGateway;
use App\Traits\PaymentGateway\Tatum;
use Illuminate\Support\Facades\Auth;
use App\Traits\PaymentGateway\Manual;
use App\Traits\PaymentGateway\Stripe;
use App\Constants\PaymentGatewayConst;
use App\Http\Helpers\PaymentGatewayApi;
use App\Models\Admin\CryptoTransaction;
use Illuminate\Support\Facades\Validator;
use App\Models\Admin\PaymentGatewayCurrency;
use KingFlamez\Rave\Facades\Rave as Flutterwave;
use App\Http\Helpers\PaymentGateway as PaymentGatewayHelper;
use App\Http\Resources\NetworkResource;

class AddMoneyController extends Controller
{
    use Stripe, Manual, Tatum;

    public function addMoneyInformation()
    {
        $user = auth()->user();

        $userWallet = UserWallet::where('user_id', $user->id)->get()->map(function ($data) {
            return [
                'balance' => (string) getAmount($data->balance, 2),
                'currency' => get_default_currency_code(),
            ];
        })->first();

        $payment_gateway = PaymentGatewayCurrency::where('payment_gateway_id', 1)->first();

        $networks = Network::all();

        $data = [
            'userWallet' => (object)$userWallet,
            'charges' => [
                'min_limit' => (string) getAmount($payment_gateway->min_limit, 2),
                'max_limit' => (string) getAmount($payment_gateway->max_limit, 2),
                'fixed_fees' => (string) getAmount($payment_gateway->fixed_charge, 2),
                'percent_fees' => (string) getAmount($payment_gateway->percent_charge, 2)
            ],
            'networks' => NetworkResource::collection($networks)
        ];
        $message =  ['success' => [__('Add Money Information!')]];
        return Helpers::success($data, $message);
    }

    public function submit(Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'network' => 'required|exists:networks,network_id',
            ]);

            if ($validator->fails()) {
                $error =  ['error' => $validator->errors()->all()];
                return Helpers::validation($error);
            }

            $payment_gateways_currencies = PaymentGatewayCurrency::where('payment_gateway_id', 1)->first();

            if (!$payment_gateways_currencies) {
                $error = ['error' => [__('Gateway Information is not available. Please provide payment gateway currency alias')]];
                return Helpers::error($error);
            }

            $defualt_currency = Currency::default();

            $user_wallet = UserWallet::auth()->where('currency_id', $defualt_currency->id)->first();

            if (!$user_wallet) {
                $error = ['error' => [__('User wallet not found')]];
                return Helpers::error($error);
            }

            $data = [
                'network' => $request->network,
                'alias' => 'add-money-usdt-usd-automatic'
            ];

            $instance = PaymentGatewayApi::init($data)->type(PaymentGatewayConst::TYPEADDMONEY)->gateway()->api()->get();

            if (!$instance) {
                $error = ['error' => [__('invalid gateway or api settings')]];
            }

            $cryptowallet = UserCryptoWallet::firstOrCreate(['user_id' => auth()->user()->id, 'currency_id' => '1', 'network' => $request->network]);

            $data = [
                'address' => $cryptowallet->address_ccp,
                'network' => $cryptowallet->network,
                'qr_code' => asset($cryptowallet->qr_code),
            ];

            return Helpers::success($data, ['success' => [__('Address generated successfully')]]);
        } catch (Exception $e) {
            $error = ['error' => [__('Something went wrong! Please try again')]];
            return Helpers::error($error);
        }
    }

    public function success(Request $request, $gateway)
    {
        $requestData = $request->all();
        $token = $requestData['token'] ?? "";
        $checkTempData = TemporaryData::where("type", $gateway)->where("identifier", $token)->first();
        if (!$checkTempData) {
            $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
            return Helpers::error($message);
        }

        $checkTempData = $checkTempData->toArray();
        try {
            PaymentGatewayApi::init($checkTempData)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive();
        } catch (Exception $e) {
            $message = ['error' => [$e->getMessage()]];
            return Helpers::error($message);
        }
        $message = ['success' => [__('Payment successful, please go back your app')]];
        return Helpers::onlysuccess($message);
    }
    public function cancel(Request $request, $gateway)
    {
        $message = ['error' => [__("Something went wrong! Please try again")]];
        return Helpers::error($message);
    }
    //stripe success
    public function stripePaymentSuccess($trx)
    {
        $token = $trx;
        $checkTempData = TemporaryData::where("type", PaymentGatewayConst::STRIPE)->where("identifier", $token)->first();
        $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];

        if (!$checkTempData) return Helpers::error($message);
        $checkTempData = $checkTempData->toArray();

        try {
            PaymentGatewayApi::init($checkTempData)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive('stripe');
        } catch (Exception $e) {
            $message = ['error' => [__("Something went wrong! Please try again")]];
            Helpers::error($message);
        }
        $message = ['success' => [__('Payment successful, please go back your app')]];
        return Helpers::onlysuccess($message);
    }
    public function flutterwaveCallback()
    {
        $status = request()->status;
        if ($status ==  'successful' || $status == 'completed') {
            $transactionID = Flutterwave::getTransactionIDFromCallback();
            $data = Flutterwave::verifyTransaction($transactionID);
            $requestData = request()->tx_ref;
            $token = $requestData;
            $checkTempData = TemporaryData::where("type", 'flutterwave')->where("identifier", $token)->first();
            $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
            if (!$checkTempData) return Helpers::error($message);

            $checkTempData = $checkTempData->toArray();
            try {
                PaymentGatewayApi::init($checkTempData)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive('flutterWave');
            } catch (Exception $e) {
                $message = ['error' => [$e->getMessage()]];
                return Helpers::error($message);
            }
            $message = ['success' => [__('Payment successful, please go back your app')]];
            return Helpers::onlysuccess($message);
        } elseif ($status ==  'cancelled') {
            $message = ['error' => [__('Payment Cancelled,please go back your app')]];
            return  Helpers::error($message);
        } else {
            $message = ['error' => ['Payment Failed,please go back your app']];
            return Helpers::error($message);
        }
    }
    //sslcommerz success
    public function sllCommerzSuccess(Request $request)
    {
        $data = $request->all();
        $token = $data['tran_id'];
        $checkTempData = TemporaryData::where("type", PaymentGatewayConst::SSLCOMMERZ)->where("identifier", $token)->first();
        $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
        if (!$checkTempData) return Helpers::error($message);
        $checkTempData = $checkTempData->toArray();

        $creator_table = $checkTempData['data']->creator_table ?? null;
        $creator_id = $checkTempData['data']->creator_id ?? null;
        $creator_guard = $checkTempData['data']->creator_guard ?? null;
        $api_authenticated_guards = PaymentGatewayConst::apiAuthenticateGuard();
        if ($creator_table != null && $creator_id != null && $creator_guard != null) {
            if (!array_key_exists($creator_guard, $api_authenticated_guards)) throw new Exception(__("Request user doesn\'t save properly. Please try again"));
            $creator = DB::table($creator_table)->where("id", $creator_id)->first();
            if (!$creator) throw new Exception(__("Request user doesn\'t save properly. Please try again"));
            $api_user_login_guard = $api_authenticated_guards[$creator_guard];
            Auth::guard($api_user_login_guard)->loginUsingId($creator->id);
        }
        if ($data['status'] != "VALID") {
            $message = ['error' => ["Added Money Failed"]];
            return Helpers::error($message);
        }
        try {
            PaymentGatewayApi::init($checkTempData)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive('sslcommerz');
        } catch (Exception $e) {
            $message = ['error' => ['Payment Failed,please go back your app']];
            return Helpers::error($message);
        }
        $message = ['success' => [__('Payment successful, please go back your app')]];
        return Helpers::onlysuccess($message);
    }
    //sslCommerz fails
    public function sllCommerzFails(Request $request)
    {
        $data = $request->all();

        $token = $data['tran_id'];
        $checkTempData = TemporaryData::where("type", PaymentGatewayConst::SSLCOMMERZ)->where("identifier", $token)->first();
        $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
        if (!$checkTempData) return Helpers::error($message);
        $checkTempData = $checkTempData->toArray();

        $creator_table = $checkTempData['data']->creator_table ?? null;
        $creator_id = $checkTempData['data']->creator_id ?? null;
        $creator_guard = $checkTempData['data']->creator_guard ?? null;

        $api_authenticated_guards = PaymentGatewayConst::apiAuthenticateGuard();
        if ($creator_table != null && $creator_id != null && $creator_guard != null) {
            if (!array_key_exists($creator_guard, $api_authenticated_guards)) throw new Exception(__("Request user doesn\'t save properly. Please try again"));
            $creator = DB::table($creator_table)->where("id", $creator_id)->first();
            if (!$creator) throw new Exception(__("Request user doesn\'t save properly. Please try again"));
            $api_user_login_guard = $api_authenticated_guards[$creator_guard];
            Auth::guard($api_user_login_guard)->loginUsingId($creator->id);
        }
        if ($data['status'] == "FAILED") {
            TemporaryData::destroy($checkTempData['id']);
            $message = ['error' => ['Payment Failed,please go back your app']];
            return Helpers::error($message);
        }
    }
    //sslCommerz canceled
    public function sllCommerzCancel(Request $request)
    {
        $data = $request->all();
        $token = $data['tran_id'];
        $checkTempData = TemporaryData::where("type", PaymentGatewayConst::SSLCOMMERZ)->where("identifier", $token)->first();
        $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
        if (!$checkTempData) return Helpers::error($message);
        $checkTempData = $checkTempData->toArray();


        $creator_table = $checkTempData['data']->creator_table ?? null;
        $creator_id = $checkTempData['data']->creator_id ?? null;
        $creator_guard = $checkTempData['data']->creator_guard ?? null;

        $api_authenticated_guards = PaymentGatewayConst::apiAuthenticateGuard();
        if ($creator_table != null && $creator_id != null && $creator_guard != null) {
            if (!array_key_exists($creator_guard, $api_authenticated_guards)) throw new Exception(__('Request user doesn\'t save properly. Please try again'));
            $creator = DB::table($creator_table)->where("id", $creator_id)->first();
            if (!$creator) throw new Exception(__("Request user doesn\'t save properly. Please try again"));
            $api_user_login_guard = $api_authenticated_guards[$creator_guard];
            Auth::guard($api_user_login_guard)->loginUsingId($creator->id);
        }
        if ($data['status'] != "VALID") {
            TemporaryData::destroy($checkTempData['id']);
            $message = ['error' => [__('Payment Cancelled,please go back your app')]];
            return Helpers::error($message);
        }
    }

    // Qrpay Call Back
    public function qrPaySuccess(Request $request)
    {

        if ($request->type ==  'success') {
            $requestData = $request->all();
            $checkTempData = TemporaryData::where("type", 'qrpay')->where("identifier", $requestData['data']['custom'])->first();

            $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
            if (!$checkTempData) return Helpers::error($message);
            $checkTempData = $checkTempData->toArray();

            try {
                PaymentGatewayApi::init($checkTempData)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive('qrpay');
            } catch (Exception $e) {
                $message = ['error' => [$e->getMessage()]];
                return Helpers::error($message);
            }
            $message = ['success' => [__('Payment successful, please go back your app')]];
            return Helpers::onlysuccess($message);
        } else {
            $message = ['error' => ['Payment Failed,please go back your app']];
            return Helpers::error($message);
        }
    }
    // QrPay Cancel
    public function qrPayCancel(Request $request, $trx_id)
    {
        $data = TemporaryData::where("identifier", $trx_id)->delete();
        if (!$data) {
            $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
            return Helpers::error($message);
        }
        $message = ['error' => [__('Payment Cancelled,please go back your app')]];
        return Helpers::onlysuccess($message);
    }
    public function perfectSuccess(Request $request, $gateway)
    {

        try {
            $token = PaymentGatewayApi::getToken($request->all(), $gateway);
            $temp_data = TemporaryData::where("type", PaymentGatewayConst::PERFECT_MONEY)->where("identifier", $token)->first();

            if (!$temp_data) {
                if (Transaction::where('callback_ref', $token)->exists()) {
                    $message = ['error' => [__("Transaction request sended successfully!")]];
                    return Helpers::onlySuccess($message);
                } else {
                    $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
                    return Helpers::error($message);
                }
            }

            $update_temp_data = json_decode(json_encode($temp_data->data), true);
            $update_temp_data['callback_data']  = $request->all();
            $temp_data->update([
                'data'  => $update_temp_data,
            ]);
            $temp_data = $temp_data->toArray();
            $instance = PaymentGatewayApi::init($temp_data)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive('perfect-money');
        } catch (Exception $e) {
            return Helpers::error(['error' => [$e->getMessage()]]);
        }
        $message = ['success' => [__('Successfully Added Money')]];
        return Helpers::onlySuccess($message);
    }

    public function perfectCancel(Request $request, $gateway)
    {
        $token = PaymentGatewayApi::getToken($request->all(), $gateway);
        $temp_data = TemporaryData::where("type", PaymentGatewayConst::TYPEADDMONEY)->where("identifier", $token)->first();
        try {
            if ($temp_data != null) {
                $temp_data->delete();
            }
        } catch (Exception $e) {
            // Handel error
        }
        $message = ['success' => [__('Added Money Canceled Successfully')]];
        return Helpers::onlySuccess($message);
    }
    //coingate
    public function coinGateSuccess(Request $request, $gateway)
    {
        try {
            $token = $request->token;
            $checkTempData = TemporaryData::where("type", PaymentGatewayConst::COINGATE)->where("identifier", $token)->first();
            if (!$checkTempData) {
                $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
                return Helpers::error($message);
            }
            if (Transaction::where('callback_ref', $token)->exists()) {
                if (!$checkTempData) {
                    $message = ['error' => [__("Transaction request sended successfully!")]];
                    return Helpers::error($message);
                }
            } else {
                if (!$checkTempData) {
                    $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
                    return Helpers::error($message);
                }
            }
            $update_temp_data = json_decode(json_encode($checkTempData->data), true);
            $update_temp_data['callback_data']  = $request->all();
            $checkTempData->update([
                'data'  => $update_temp_data,
            ]);
            $temp_data = $checkTempData->toArray();
            PaymentGatewayApi::init($temp_data)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive('coingate');
        } catch (Exception $e) {
            $message = ['error' => [$e->getMessage()]];
            return Helpers::error($message);
        }
        $message = ['success' => [__('Successfully Added Money')]];
        return Helpers::onlysuccess($message);
    }
    public function coinGateCancel(Request $request, $gateway)
    {
        if ($request->has('token')) {
            $identifier = $request->token;
            if ($temp_data = TemporaryData::where('identifier', $identifier)->first()) {
                $temp_data->delete();
            }
        }
        $message = ['success' => [__('Added Money Canceled Successfully')]];
        return Helpers::onlysuccess($message);
    }
    public function cryptoPaymentConfirm(Request $request, $trx_id)
    {
        $transaction = Transaction::where('trx_id', $trx_id)->where('status', PaymentGatewayConst::STATUSWAITING)->first();
        if (!$transaction) {
            $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
            return Helpers::error($message);
        }
        $user =  $transaction->user;
        $gateway_currency =  $transaction->currency->alias;
        $request_data = $request->merge([
            'currency' => $gateway_currency,
            'amount' => $transaction->request_amount,
        ]);
        $output = PaymentGatewayHelper::init($request_data->all())->type(PaymentGatewayConst::TYPEADDMONEY)->gateway()->get();


        $dy_input_fields = $transaction->details->payment_info->requirements ?? [];
        $validation_rules = $this->generateValidationRules($dy_input_fields);


        $validator = [];
        if (count($validation_rules) > 0) {
            $validator = Validator::make($request->all(), $validation_rules);
            if ($validator->fails()) {
                $error =  ['error' => $validator->errors()->all()];
                return Helpers::validation($error);
            }
            $validated =  $validator->validate();
        }

        if (!isset($validated['txn_hash'])) {
            $message = ['error' => [__('Transaction hash is required for verify')]];
            return Helpers::error($message);
        }

        $receiver_address = $transaction->details->payment_info->receiver_address ?? "";

        // check hash is valid or not
        $crypto_transaction = CryptoTransaction::where('txn_hash', $validated['txn_hash'])
            ->where('receiver_address', $receiver_address)
            ->where('asset', $transaction->gateway_currency->currency_code)
            ->where(function ($query) {
                return $query->where('transaction_type', "Native")
                    ->orWhere('transaction_type', "native");
            })
            ->where('status', PaymentGatewayConst::NOT_USED)
            ->first();

        if (!$crypto_transaction) {
            $message = ['error' => [__('Transaction hash is not valid! Please input a valid hash')]];
            return Helpers::error($message);
        }

        if ($crypto_transaction->amount >= $transaction->total_payable == false) {
            if (!$crypto_transaction) {
                $message = ['error' => [__("Insufficient amount added. Please contact with system administrator")]];
                return Helpers::error($message);
            }
        }

        DB::beginTransaction();
        try {

            // Update user wallet balance
            DB::table($transaction->creator_wallet->getTable())
                ->where('id', $transaction->creator_wallet->id)
                ->increment('balance', $transaction->request_amount);

            // update crypto transaction as used
            DB::table($crypto_transaction->getTable())->where('id', $crypto_transaction->id)->update([
                'status'        => PaymentGatewayConst::USED,
            ]);

            // update transaction status
            $transaction_details = json_decode(json_encode($transaction->details), true);
            $transaction_details['payment_info']['txn_hash'] = $validated['txn_hash'];

            DB::table($transaction->getTable())->where('id', $transaction->id)->update([
                'details'       => json_encode($transaction_details),
                'status'        => PaymentGatewayConst::STATUSSUCCESS,
                'available_balance'        => $transaction->available_balance + $transaction->request_amount,
            ]);
            //notification
            $notification_content = [
                'title'         => "Add Money",
                'message'       => "Your Wallet" . " (" . $output['wallet']->currency->code . ")  " . "balance  has been added" . " " . $output['amount']->requested_amount . ' ' . $output['wallet']->currency->code,
                'time'          => Carbon::now()->diffForHumans(),
                'image'         => get_image($user->image, 'user-profile'),
            ];

            UserNotification::create([
                'type'      => NotificationConst::BALANCE_ADDED,
                'user_id'  =>  $user->id,
                'message'   => $notification_content,
            ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            $message = ['error' => [__('Something went wrong! Please try again')]];
            return Helpers::error($message);
        }

        $message = ['success' => [__('Payment Confirmation Success')]];
        return Helpers::onlysuccess($message);
    }
    public function redirectBtnPay(Request $request, $gateway)
    {
        try {
            return PaymentGatewayApi::init([])->handleBtnPay($gateway, $request->all());
        } catch (Exception $e) {
            $message = ['error' => [$e->getMessage()]];
            return Helpers::error($message);
        }
    }
    public function successGlobal(Request $request, $gateway)
    {
        try {
            $token = PaymentGatewayApi::getToken($request->all(), $gateway);
            $temp_data = TemporaryData::where("identifier", $token)->first();

            if (!$temp_data) {
                if (Transaction::where('callback_ref', $token)->exists()) {
                    $message = ['error' => [__('Transaction request sended successfully!')]];
                    return Helpers::error($message);
                } else {
                    $message = ['error' => [__('Transaction failed. Record didn\'t saved properly. Please try again')]];
                    return Helpers::error($message);
                }
            }

            $update_temp_data = json_decode(json_encode($temp_data->data), true);
            $update_temp_data['callback_data']  = $request->all();
            $temp_data->update([
                'data'  => $update_temp_data,
            ]);
            $temp_data = $temp_data->toArray();
            $instance = PaymentGatewayApi::init($temp_data)->type(PaymentGatewayConst::TYPEADDMONEY)->responseReceive($temp_data['type']);

            // return $instance;
        } catch (Exception $e) {
            $message = ['error' => [$e->getMessage()]];
            return Helpers::error($message);
        }
        $message = ['success' => [__('Successfully Added Money')]];
        return Helpers::onlysuccess($message);
    }
    public function cancelGlobal(Request $request, $gateway)
    {
        $token = PaymentGatewayApi::getToken($request->all(), $gateway);
        $temp_data = TemporaryData::where("identifier", $token)->first();
        try {
            if ($temp_data != null) {
                $temp_data->delete();
            }
        } catch (Exception $e) {
            // Handel error
        }
        $message = ['success' => [__('Added Money Canceled Successfully')]];
        return Helpers::error($message);
    }
    public function postSuccess(Request $request, $gateway)
    {
        try {
            $token = PaymentGatewayApi::getToken($request->all(), $gateway);
            $temp_data = TemporaryData::where("identifier", $token)->first();
            if ($temp_data && $temp_data->data->creator_guard != 'api') {
                Auth::guard($temp_data->data->creator_guard)->loginUsingId($temp_data->data->creator_id);
            }
        } catch (Exception $e) {
            $message = ['error' => [$e->getMessage()]];
            return Helpers::error($message);
        }

        return $this->successGlobal($request, $gateway);
    }
    public function postCancel(Request $request, $gateway)
    {
        try {
            $token = PaymentGatewayApi::getToken($request->all(), $gateway);
            $temp_data = TemporaryData::where("identifier", $token)->first();
            if ($temp_data && $temp_data->data->creator_guard != 'api') {
                Auth::guard($temp_data->data->creator_guard)->loginUsingId($temp_data->data->creator_id);
            }
        } catch (Exception $e) {
            $message = ['error' => [$e->getMessage()]];
            return Helpers::error($message);
        }

        return $this->cancelGlobal($request, $gateway);
    }
}
