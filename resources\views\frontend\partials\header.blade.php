<!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    Start Header
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->

@php
    $color = @$basic_settings->base_color ?? '#000000';
@endphp

<style>
    :root {
        --primary-color: {{ $color }};
    }
</style>

@php
    $type = App\Constants\GlobalConst::SETUP_PAGE;
    $menues = DB::table('setup_pages')->where('status', 1)->where('type', Str::slug($type))->get();
@endphp
{{-- <header class="header-section">
    <div class="header">
        <div class="header-bottom-area">
            <div class="container custom-container">
                <div class="header-menu-content">
                    <nav class="navbar navbar-expand-lg p-0">
                          <a class="site-logo site-title sidebar-main-logo" href="{{ setRoute('index') }}">
                    <img src="{{ get_logo($basic_settings) }}"  data-white_img="{{ get_logo($basic_settings,'white') }}"
                        data-dark_img="{{ get_logo($basic_settings,'dark') }}"
                        alt="site-logo"
                        id="sidebar-logo">
                </a>

                        <button class="navbar-toggler ms-auto" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                            <span class="fas fa-bars"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarSupportedContent">
                            <ul class="navbar-nav main-menu ms-auto">
                                @php
                                $current_url = URL::current();
                            @endphp
                            @foreach ($menues as $item)
                                @php
                                    $title = json_decode($item->title);
                                @endphp
                                <li><a href="{{ url($item->url) }}" class="@if ($current_url == url($item->url)) active @endif">{{ __($title->title) }}</a></li>
                            @endforeach
                            </ul>
                            @php
                                $session_lan = session('local')??get_default_language_code();
                            @endphp
                            <select class="language-select langSel">
                                @foreach ($__languages as $item)
                                    <option value="{{$item->code}}" @if ($session_lan == $item->code) selected  @endif>{{ $item->name }}</option>
                                @endforeach
                            </select>
                            <div class="header-action">
                                @auth
                                    @if (auth()->user()->email_verified == 0)
                                    <button class="btn--base header-account-btn">{{ __("Login Now") }}</button>
                                    @else
                                     <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{__("Dashboard")}}</a>
                                    @endif

                                @else
                                <button class="btn--base header-account-btn">{{ __("Login Now") }}</button>
                                @endauth
                            </div>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</header> --}}


<nav class="navbar navbar-expand-lg py-4">
    <div class="container-fluid">
        <div class="navbar-brand mx-auto">
            <a href="{{ setRoute('index') }}">
                {{-- <img src="{{ asset('frontend/assets/logo.svg') }}" alt="Zid Cash" /> --}}

                <img src="{{ get_logo($basic_settings) }}" data-white_img="{{ get_logo($basic_settings, 'white') }}"
                    alt="site-logo" id="header-logo">
            </a>
        </div>
        <div class="collapse navbar-collapse" id="navbarSupportedContent" style=''>
            <ul class="navbar-nav mx-auto mb-2 mb-lg-0">
                <li class="nav-item">
                    <a href="{{ setRoute('index') }}" class="nav-link active" id=''>{{ __('Home') }}</a>
                </li>
                <li class="nav-item">
                    <a href="{{ setRoute('about') }}" class="nav-link" id='about'>{{ __('About Us') }}</a>
                </li>
                <li class="nav-item">
                    <a href="{{ setRoute('services') }}" class="nav-link" id='services'>{{ __('Our Services') }}</a>
                </li>
                <li class="nav-item">
                    <a href="{{ setRoute('contact') }}" class="nav-link" id='contact'>{{ __('CONTACT US') }}</a>
                </li>
                {{-- @php
                    $session_lan = session('local') ?? get_default_language_code();
                @endphp --}}


                <!-- language start -->
                <div class="language-dropdown d-flex align-items-center justify-content-center">
                    {{-- <button class="dropdown-btn">
                        <i class="las la-globe-africa navbar-wrapper__icon"></i>
                    </button> --}}
                    <ul class="dropdown-menu p-2">
                        @foreach ($__languages as $item)
                            <li class='nav-item'>
                                <a href="{{ route('lang', ['lang' => $item->code]) }}"
                                    data-value="{{ $item->code }}" style="width:100%;margin:5px"
                                    class="@if (app()->getLocale() == $item->code) selected @endif">{{ $item->name }}</a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <!-- language end -->

            </ul>
            <div class="header-action">
                <!-- language start -->
                <div class="language-dropdown">
                    <button class="dropdown-btn d-flex">
                        <span>
                            {{ app()->getLocale() == 'en' ? 'English' : 'العربية' }}
                        </span>
                        <svg style="width: 18px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
                            <path
                                d="M297.4 470.6C309.9 483.1 330.2 483.1 342.7 470.6L534.7 278.6C547.2 266.1 547.2 245.8 534.7 233.3C522.2 220.8 501.9 220.8 489.4 233.3L320 402.7L150.6 233.4C138.1 220.9 117.8 220.9 105.3 233.4C92.8 245.9 92.8 266.2 105.3 278.7L297.3 470.7z"
                                fill="#6e6e6e" />
                        </svg>
                    </button>
                    <ul class="dropdown-menu p-2">
                        @if (app()->getLocale() == 'ar')
                            <li class='nav-item'>
                                <a href='{{ route('lang', ['lang' => 'en']) }}'>English</a>
                            </li>
                        @else
                            <li class='nav-item'>
                                <a href='{{ route('lang', ['lang' => 'ar']) }}'>العربية</a>
                            </li>
                        @endif
                    </ul>
                </div>
                <!-- language end -->
                @auth
                    @if (auth()->user()->email_verified == 0)
                        <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
                    @else
                        <a href="{{ setRoute('user.dashboard') }}" class="btn--base">{{ __('Dashboard') }}</a>
                    @endif
                @else
                    <button class="btn--base header-account-btn">{{ __('Start Now') }}</button>
                @endauth
            </div>
        </div>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon bg-transparent">

                <img src="{{ asset('frontend/assets/icons/burger.svg') }}" alt="burger"
                    style='object-fit:cover; width: 44px;'>
            </span>
        </button>
    </div>
</nav>

<div class="whatsapp_floating_btn">
    <a href="https://wa.me/************" style="width:56px; height:56px">
        <img src="{{ asset('frontend/assets/whatsapp_nav.png') }}" alt="whatsapp" />
    </a>
</div>


<script>
    //  ! start
    var dropdownBtn = document.querySelector(".language-dropdown .dropdown-btn");
    var dropdownMenu = document.querySelector(".language-dropdown .dropdown-menu");
    var dropdownItems = document.querySelectorAll(".language-dropdown .dropdown-menu li");



    // Toggle the dropdown menu visibility
    dropdownBtn.addEventListener("click", function() {
        dropdownMenu.classList.toggle("show");
    });

    // Handle dropdown item click
    dropdownItems.forEach(function(item) {
        item.addEventListener("click", function() {
            var selectedLanguage = this.textContent;
            var selectedValue = this.getAttribute("data-value");

            dropdownBtn.textContent = selectedLanguage;
            dropdownItems.forEach(function(li) {
                li.classList.remove("selected");
            });
            this.classList.add("selected");

            dropdownMenu.classList.remove("show"); // Hide the dropdown after selection
        });
    });

    // Close the dropdown if clicked outside
    document.addEventListener("click", function(e) {
        if (!dropdownBtn.contains(e.target) && !dropdownMenu.contains(e.target)) {
            dropdownMenu.classList.remove("show");
        }
    });
    //  ! en
</script>
