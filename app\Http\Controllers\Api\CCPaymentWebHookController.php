<?php

namespace App\Http\Controllers\Api;

use App\Models\Admin\Currency;
use Exception;
use Stripe\PaymentMethod;
use App\Models\CCPaymentLog;
use Illuminate\Http\Request;
use App\Models\UserCryptoWallet;
use App\Services\CCPaymentService;
use App\Http\Controllers\Controller;
use App\Models\Admin\PaymentGateway;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class CCPaymentWebHookController extends Controller
{
    public function paymentCallback(Request $request)
    {
        try {
            
            //add ccpaymentLog

            $ccpayment_log = CCPaymentLog::create([
                'payload' => json_encode($request->all())
            ]);

            if ($request->type != "DirectDeposit") {
                $ccpayment_log->message = $request["msg"]["status"];
                $ccpayment_log->save();
                return response("success", 200);
            }

            if ($request->msg["status"] != "Success") {
                $ccpayment_log->message = $request->msg["status"];
                $ccpayment_log->save();
                return response("success", 200);
            }

            if ($request->msg["coinSymbol"] != "USDT") {
                $ccpayment_log->message = "the crypto used is " . $request->msg["coinSymbol"];
                $ccpayment_log->save();
                return response("success", 200);
            }

            $response = $this->sendRequest($request->msg["recordId"]);

            if ($response["msg"] == "success") {

                $record = $response["data"]["record"];

                if ($record["status"] == "Success") {

                    $paid_amount = $record["amount"] - $record["serviceFee"];

                    // Log::error($record['amount']);
                    // Log::error($record["serviceFee"]);
                    // Log::error($paid_amount);

                    $to_address = $record["toAddress"];
                    $fee = $record["serviceFee"];
                    $txid = $record["txId"];
                    $ccpayment_log->transaction_id = $txid;

                    $existPaymentLog = CCPaymentLog::where(['transaction_id' => $txid, 'status' => 'Success'])->first();
                    if (isset($existPaymentLog)) {
                        return response("success", 200);
                    }

                    $user_wallet = UserCryptoWallet::where('address_ccp', $to_address)->first();
                    if (!isset($user_wallet)) {
                        $ccpayment_log->message = "unkown user address";
                        $ccpayment_log->save();
                        return response("success", 200);
                    }

                    $method_id = 1;  // USDT Automatic

                    // payment is complete or queued for nightly payout, success
                    $currency = Currency::find($user_wallet->currency_id);
                    $data['user_id'] = $user_wallet->user_id;
                    $data['currency_code'] = $currency->code;
                    $data['currency_id'] = $user_wallet->currency_id;

                    $data['amount'] = $paid_amount;
                    $data['method_id'] = $method_id;
                    $data['fees'] = $fee;
                    $data["txid"] = $txid;

                    $ccpayment_log->status = "Success";
                    $ccpayment_log->message = CCPaymentService::createDepositAndTransaction($data);
                    $ccpayment_log->save();

                    return response("success", 200);
                }
            }
            return response("failed", 400);
        } catch (Exception $e) {
            Log::error($e);
            $ccpayment_log->message = $e->getMessage();
            $ccpayment_log->payload = json_encode($e);
            $ccpayment_log->save();
        }
    }


    public function sendRequest($recordId)
    {
        $payment = PaymentGateway::first();

        $app_id = $payment->credentials[0]->value;
        $app_secret = $payment->credentials[1]->value;

        $url = "https://ccpayment.com/ccpayment/v2/getAppDepositRecord";
        $content = ["recordId" => $recordId];

        $timestamp = time();
        $body = json_encode($content);
        $sign_text = $app_id . $timestamp . $body;


        $server_sign = hash_hmac('sha256', $sign_text, $app_secret);

        $response = Http::withHeaders([
            'Content-Type' => 'application/json;charset=utf-8',
            'Appid' => $app_id,
            'Sign' => $server_sign,
            'Timestamp' => $timestamp,
        ])->post($url, $content);


        $result = $response->json();

        Log::info($result);

        return $result;
    }
}
