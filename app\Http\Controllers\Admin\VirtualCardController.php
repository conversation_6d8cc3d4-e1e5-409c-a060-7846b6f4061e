<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\VirtualCard;
use App\Models\VirtualCardApi;
use App\Models\VirtualCardSetting;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class VirtualCardController extends Controller
{

    public function __construct()
    {
        $this->middleware('can:show virtual cards')->only('cardApi', 'transactionLogs');
        $this->middleware('can:update virtual card settings')->only('cardApiUpdate');
    }

    public function cardApi()
    {
        $page_title = __("Setup Virtual Card Api");
        $api = VirtualCardSetting::first();
        return view('admin.sections.virtual-card.api', compact(
            'page_title',
            'api',
        ));
    }
    public function cardApiUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'api_key'              => 'required|string',
            'min_amount'           => 'required|numeric|min:0|lte:max_amount',
            'max_amount'           => 'required|numeric|min:0',
            'max_limit'            => 'required|numeric',
            'fixed_creation_fee'   => 'required|numeric',
            'percent_creation_fee' => 'required|numeric',
            'fixed_recharge_fee'   => 'required|numeric',
            'percent_recharge_fee' => 'required|numeric',
        ]);

        $validator->after(function ($validator) use ($request) {
            $creationFee = $request->fixed_creation_fee + (($request->percent_creation_fee / 100) * $request->min_amount);
            $rechargeFee = $request->fixed_recharge_fee + (($request->percent_recharge_fee / 100) * $request->min_amount);

            if ($request->min_amount < $creationFee) {
                $validator->errors()->add('min_amount', __('The minimum amount must be at least :value to cover creation fees.', ['value' => $creationFee]));
            }

            if ($request->min_amount < $rechargeFee) {
                $validator->errors()->add('min_amount', __('The minimum amount must be at least :value to cover recharge fees.', ['value' => $rechargeFee]));
            }
        });

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $request->except('_token', '_method', 'image');
        $api = VirtualCardSetting::first();

        // Update the model instance with the validated data
        $api->fill($data);

        $api->save();

        return back()->with(['success' => [__('Card API Has Been Updated.')]]);
    }


    public function transactionLogs()
    {
        $page_title = __("Virtual Card Logs");
        $transactions = Transaction::with(
            'user:id,firstname,lastname,email,username,full_mobile',
            'currency:id,name',
        )->where('type', 'VIRTUAL-CARD')->latest()->paginate(20);

        return view('admin.sections.virtual-card.logs', compact(
            'page_title',
            'transactions'
        ));
    }
}
