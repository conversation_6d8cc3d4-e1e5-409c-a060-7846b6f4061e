@php
    $lang = selectedLang();
    $footer_slug = Illuminate\Support\Str::slug(App\Constants\SiteSectionConst::FOOTER_SECTION);
    $footer = App\Models\Admin\SiteSections::getData($footer_slug)->first();
    $type = Illuminate\Support\Str::slug(App\Constants\GlobalConst::USEFUL_LINKS);
    $policies = App\Models\Admin\SetupPage::orderBy('id')->where('type', $type)->where('status', 1)->get();

@endphp
{{-- 


        @php
        $color = @$basic_settings->base_color ?? '#000000';
    @endphp

    <style>
    :root {
        --primary-color: {{$color}};
    }
    </style>
<footer class="footer-section pt-80 ">
    <div class="container">
        <div class="footer-top-area">
            <div class="footer-widget-wrapper">
                        <div class="futter-logo">
                            <a class="site-logo site-title" href="{{ setRoute('index') }}">
                                <img src="{{ get_logo($basic_settings) }}"  data-white_img="{{ get_logo($basic_settings,'white') }}"
                                data-dark_img="{{ get_logo($basic_settings,'dark') }}"
                                    alt="site-logo">
                            </a>
                        </div>
                    <div class="col-lg-6">
                        <P>{{ __(@$footer->value->language->$lang->details) }}</P>
                    </div>
                    <div class="col-lg-3">
                        <ul class="footer-list">
                            @foreach ($policies ?? [] as $key => $data)
                            <li><a href="{{ setRoute('useful.link',$data->slug) }}">{{ @$data->title->language->$lang->title }}</a></li>
                            @endforeach

                        </ul>
                    </div>
              </div>
         </div>
        <div class="footer-bottom-area d-flex justify-content-between">
            <div class="copyright-area">
                <p>{{ __(@$footer->value->language->$lang->footer_text) }} <a class="fw-bold" href="{{ setRoute('index') }}"><span>{{ $basic_settings->site_name }}</span></a></p>
            </div>
            <div class="social-area">
                <ul class="footer-social">
                    @if (isset($footer->value->items))
                    @foreach ($footer->value->items ?? [] as $key => $item)
                    <li><a href="{{ @$item->language->$lang->link }}" target="_blank"><i class="{{ @$item->language->$lang->social_icon }}"></i></a></li>
                    @endforeach
                @endif
                </ul>
            </div>
        </div>
    </div>
</footer> --}}

<footer class="footer bg-light py-5">
    <div id="container" class="container">
        <div class="mb-4">
            <a href="{{ setRoute('index') }}">
                {{-- <img src="{{ asset('frontend/assets/logo.svg') }}" alt="{{ __('logo_image_alt') }}" /> --}}
                <img src="{{ get_logo($basic_settings) }}" data-white_img="{{ get_logo($basic_settings, 'white') }}"
                    alt="site-logo" id="header-logo">
            </a>
        </div>

        <div class="row">
            <div class="col-12 col-md-6 mb-3 mb-md-0 text-md-start">
                <p id="footer__desc" class="mb-0">
                    {{ __('create_virtual_card_footer') }}
                </p>
            </div>
            <div class="col-12 col-md-6 d-flex justify-content-center justify-content-md-end px-0">
                <ul class="list-unstyled d-flex gap-3">
                    <li>
                        <a href="{{ setRoute('index') }}" aria-current="page" class="fs-6">{{ __('home') }}</a>
                    </li>
                    <li>
                        <a href="{{ setRoute('about') }}" class="fs-6">{{ __('about_us') }}</a>
                    </li>
                    <li>
                        <a href="{{ setRoute('services') }}" class="fs-6">{{ __('our_services') }}</a>
                    </li>
                    <li>
                        <a target='_blank' href="{{ setRoute('privacy.policy') }}" class="fs-6">
                            {{ __('Terms Of Use & Privacy Policy') }} </a>
                    </li>
                    <li>
                        <a href="{{ setRoute('contact') }}" class="fs-6">{{ __('contact_us') }}</a>
                    </li>
                </ul>
            </div>
        </div>

        <div id="divider" class="my-4"></div>

        <div class="d-flex flex-column flex-md-row justify-content-between align-items-center">
            <p class="mb-3 mb-md-0 text-center text-md-start">
                {{ __('All intellectual property rights are owned by Zid Cash. The project is implemented by') }}
                <a target="_blank" href="https://geexar.com/" class=" fw-bold" style="color: var(--primary-color);">
                    Geexar
                </a>
            </p>
            <ul class="list-unstyled d-flex gap-3 justify-content-center justify-content-md-end">
                <li>
                    <a href="#" style="width:20px; height:20px">
                        <img src="{{ asset('frontend/assets/google.svg') }}" alt="Google" />
                    </a>
                </li>
                <li>
                    <a href="#" style="width:20px; height:20px">
                        <img src="{{ asset('frontend/assets/youtube.svg') }}" alt="Youtube" />
                    </a>
                </li>
                <li>
                    <a href="#" style="width:20px; height:20px">
                        <img src="{{ asset('frontend/assets/Twitter.svg') }}" alt="Twitter" />
                    </a>
                </li>
                <li>
                    <a href="#" style="width:20px; height:20px">
                        <img src="{{ asset('frontend/assets/Facebook.svg') }}" alt="facebook" />
                    </a>
                </li>
                <li>
                    <a href="https://wa.me/447451296080" style="width:18px; height:18px">
                        <img src="{{ asset('frontend/assets/whatsapp.svg') }}" alt="whatsapp" />
                    </a>
                </li>
            </ul>
        </div>
    </div>
</footer>
