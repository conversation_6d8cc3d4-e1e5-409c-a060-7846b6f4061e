<?php

namespace App\Http\Controllers\Api\User;

use Exception;
use App\Models\UserWallet;
use App\Models\Transaction;
use Illuminate\Http\Request;
use App\Constants\GlobalConst;
use App\Models\VirtualCardApi;
use App\Http\Helpers\Api\Helpers;
use App\Models\UserAuthorization;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use App\Constants\PaymentGatewayConst;
use App\Models\Admin\TransactionSetting;
use App\Http\Resources\User\AddMoneyLogs;
use App\Http\Resources\User\GiftCardLogs;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use App\Http\Resources\User\VirtualCardLogs;
use App\Http\Resources\User\WithdrawMoneyLog;
use App\Http\Resources\User\AddSubBalanceLogs;
use App\Http\Resources\User\TransferMoneyLogs;
use App\Providers\Admin\BasicSettingsProvider;
use App\Http\Resources\Api\User\TransactionResource;
use App\Notifications\User\Auth\SendAuthorizationCode;

class UserController extends Controller
{
    protected $api;
    public function __construct()
    {
        $cardApi = VirtualCardApi::first();
        $this->api =  $cardApi;
    }
    public function home()
    {
        $user = auth()->user();
        $totalAddMoney = Transaction::auth()->addMoney()->where('status', 1)->sum('request_amount');
        $virtualCards = activeCardData()['active_cards'];
        $userWallet = UserWallet::where('user_id', $user->id)->get()->map(function($data) {
            return [
                'balance' => getAmount($data->balance, 2),
                'currency' => get_default_currency_code(),
            ];
        })->first();

        $transactions = Transaction::auth()->latest()->take(5)->get()->map(function($item) {
            $commonData = [
                'id' => $item->id,
                'type' => $item->attribute,
                'trx' => $item->trx_id,
                'transaction_type' => __($item->type),
                'request_amount' => getAmount($item->request_amount, 2) . ' ' . get_default_currency_code(),
                'payable' => getAmount($item->payable, 2) . ' ' . ($item->currency->currency_code ?? get_default_currency_code()),
                'remark' => $item->remark ?? "",
                'status' => $item->stringStatus->value,
                'date_time' => $item->created_at,
            ];

            if ($item->stringStatus->value == 'Rejected' || $item->stringStatus->value == 'مرفوض') {
                $commonData['rejection_reason'] = $item->reject_reason;
            }

            switch ($item->type) {
                case payment_gateway_const()::TYPEADDMONEY:
                case payment_gateway_const()::WITHDRAWMONEY:
                case payment_gateway_const()::GIFTCARD:
                case payment_gateway_const()::TYPEADDSUBTRACTBALANCE:
                    return $commonData;

                case payment_gateway_const()::TYPETRANSFERMONEY:
                    if ($item->attribute == payment_gateway_const()::RECEIVED) {
                        return $commonData;
                    } elseif ($item->attribute == payment_gateway_const()::SEND) {
                        $commonData['recipient_received'] = getAmount($item->details->recipient_amount, 2) . ' ' . get_default_currency_code();
                        return $commonData;
                    }
                    break;

                case payment_gateway_const()::VIRTUALCARD:
                    $commonData['transaction_type'] = __("Virtual Card") . ' (' . __($item->remark) . ')';
                    return $commonData;
            }

            return null;
        });

        $cardCreateCharge = TransactionSetting::where('slug', 'virtual_card')->where('status', 1)->get()->map(function($data) {
            return [
                'id' => $data->id,
                'slug' => $data->slug,
                'title' => $data->title,
                'fixed_charge' => getAmount($data->fixed_charge, 2),
                'percent_charge' => getAmount($data->percent_charge, 2),
                'min_limit' => getAmount($data->min_limit, 2),
                'max_limit' => getAmount($data->max_limit, 2),
            ];
        })->first();

        $cardReloadCharge = TransactionSetting::where('slug', 'reload_card')->where('status', 1)->get()->map(function($data) {
            return [
                'id' => $data->id,
                'slug' => $data->slug,
                'title' => $data->title,
                'fixed_charge' => getAmount($data->fixed_charge, 2),
                'percent_charge' => getAmount($data->percent_charge, 2),
                'min_limit' => getAmount($data->min_limit, 2),
                'max_limit' => getAmount($data->max_limit, 2),
            ];
        })->first();

        $data = [
            'default_image' => "public/backend/images/default/profile-default.webp",
            'image_path' => "public/frontend/user",
            'user' => $user,
            'base_curr' => get_default_currency_code(),
            'userWallet' => (object)$userWallet,
            'active_virtual_system' => activeCardSystem(),
            'card_create_charge' => $cardCreateCharge,
            'card_reload_charge' => $cardReloadCharge,
            'totalAddMoney' => getAmount($totalAddMoney, 2) . ' ' . get_default_currency_code(),
            'active_cards' => $virtualCards,
            'transactions' => $transactions->filter(),
        ];

        $message = ['success' => [__('User Dashboard')]];
        return Helpers::success($data, $message);
    }


    public function profile(){
        $user = auth()->user();
        $data =[
            'default_image'    => "public/backend/images/default/profile-default.webp",
            "image_path"  =>  "public/frontend/user",
            'user'         =>   $user,
            'countries' =>get_all_countries()
        ];
        $message =  ['success'=>[__("User Profile")]];
        return Helpers::success($data,$message);
    }
    public function profileUpdate(Request $request)
    {
        $user = auth()->user();

        // Validate the request
        $validator = Validator::make($request->all(), [
            'firstname'  => "sometimes|string|max:60",
            'lastname'   => "sometimes|string|max:60",
            'email'      => "sometimes|string|email",
            'country'    => "sometimes|string|max:50",
            'state'      => "sometimes|string|max:50",
            'city'       => "sometimes|string|max:50",
            'zip_code'   => "sometimes|numeric",
            'address'    => "sometimes|string|max:250",
            'image'      => "sometimes|image|mimes:jpg,png,svg,webp|max:10240",
        ]);

        if ($validator->fails()) {
            $error = ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }

        // Merge the new data with the existing user data
        $data = $request->all();

        // Convert address to an array if it's an object
        $currentAddress = is_object($user->address) ? (array)$user->address : $user->address;

        $validated = [
            'firstname' => $data['firstname'] ?? $user->firstname,
            'lastname'  => $data['lastname'] ?? $user->lastname,
            'address'   => [
                'country' => $data['country'] ?? $currentAddress['country'] ?? '',
                'state'   => $data['state'] ?? $currentAddress['state'] ?? '',
                'city'    => $data['city'] ?? $currentAddress['city'] ?? '',
                'zip'     => $data['zip_code'] ?? $currentAddress['zip'] ?? '',
                'address' => $data['address'] ?? $currentAddress['address'] ?? '',
            ],
            'image'     => $user->image,  // Default to the current image
        ];

        if ($request->hasFile("image")) {
            $oldImage = $user->image == 'default.png' ? null : $user->image;
            $image = upload_file($data['image'], 'user-profile', $oldImage);
            $upload_image = upload_files_from_path_dynamic([$image['dev_path']], 'user-profile');
            delete_file($image['dev_path']);
            $validated['image'] = $upload_image;
        }

        try {
            if ($request->has('email') && $data['email'] !== $user->email) {
                $data = [
                    'user_id'    => $user->id,
                    'code'       => generate_random_code(),
                    'token'      => generate_unique_string("user_authorizations", "token", 200),
                    'created_at' => now(),
                ];
                DB::table("user_authorizations")->insert($data);

                $user->notify(new SendAuthorizationCode((object)$data));
                return Helpers::success(['code' => $data['code']], __('Verification code sent successfully'));
            } else {
                $user->update($validated);
            }
        } catch (\Exception $e) {
            $error = ['error' => [__("Something went wrong! Please try again")]];
            return Helpers::error($error);
        }

        $message = ['success' => [__("Profile successfully updated!")]];
        return Helpers::onlysuccess($message);
    }


    public function verifyCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|numeric',
        ]);
        if($validator->fails()){
            $error =  ['error'=>$validator->errors()->all()];
            return Helpers::validation($error);
        }
        $user = auth()->user();
        $code = $request->code;
        $otp_exp_sec = BasicSettingsProvider::get()->otp_exp_seconds ?? GlobalConst::DEFAULT_TOKEN_EXP_SEC;
        $auth_column = UserAuthorization::where("user_id",$user->id)->where("code",$code)->first();

        if(!$auth_column){
             $error = ['error'=>[__('Verification code does not match')]];
            return Helpers::error($error);
        }
        if($auth_column->created_at->addSeconds($otp_exp_sec) < now()) {
            $error = ['error'=>[__('Session expired. Please try again')]];
            return Helpers::error($error);
        }
        try{

        }catch(Exception $e) {
            $error = ['error'=>[__('Something went wrong! Please try again')]];
            return Helpers::error($error);
        }
        $message =  ['success'=>[__('mail successfully verified')]];
        return Helpers::onlysuccess($message);
    }
    public function passwordUpdate(Request $request) {

        $basic_settings = BasicSettingsProvider::get();
        $passowrd_rule = "required|string|min:6|confirmed";
        if($basic_settings->secure_password) {
            $passowrd_rule = ["required",Password::min(8)->letters()->mixedCase()->numbers()->symbols()->uncompromised(),"confirmed"];
        }
        $validator = Validator::make($request->all(), [
            'current_password'      => "required|string",
            'password'              => $passowrd_rule,
        ]);
        if($validator->fails()){
            $error =  ['error'=>$validator->errors()->all()];
            return Helpers::validation($error);
        }
        if(!Hash::check($request->current_password,auth()->user()->password)) {
            $error = ['error'=>[__('Current password didn\'t match')]];
            return Helpers::error($error);
        }

        try{
            auth()->user()->update([
                'password'  => Hash::make($request->password),
            ]);
        }catch(Exception $e) {
            $error = ['error'=>[__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
        $message =  ['success'=>[__("Password successfully updated!")]];
        return Helpers::onlysuccess($message);

    }
    public function deleteAccount(Request $request) {
        $user = auth()->user();
        $user->status = false;
        $user->email_verified = false;
        $user->sms_verified = false;
        $user->kyc_verified = false;
        $user->deleted_at = now();
        $user->save();
        try{
            $user->token()->revoke();
            $message =  ['success'=>[__('User deleted successfully')]];
            return Helpers::onlysuccess($message);
        }catch(Exception $e) {
            $error = ['error'=>[__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }
    public function transactions(){
        // start transaction now
        $addMoney           = Transaction::auth()->addMoney()->orderByDesc("id")->latest()->get();
        $transferMoney      = Transaction::auth()->transferMoney()->orderByDesc("id")->get();
        $virtualCard        = Transaction::auth()->virtualCard()->orderByDesc("id")->get();
        $withdrawMoney      = Transaction::auth()->withdrawMoney()->orderByDesc("id")->get();
        $addSubBalance      = Transaction::auth()->addSubBalance()->orderByDesc("id")->get();

        //dd($withdrawMoney->pluck('id')->toArray());

        $transactions = [
            'add_money'         => AddMoneyLogs::collection($addMoney),
            'send_money'        => TransferMoneyLogs::collection($transferMoney),
            'virtual_card'      => VirtualCardLogs::collection($virtualCard),
            'withdraw_money'    => WithdrawMoneyLog::collection($withdrawMoney),
            'add_sub_balance'   => AddSubBalanceLogs::collection($addSubBalance),

        ];
        $transactions = (object)$transactions;

        $transaction_types = [
            'add_money'         => PaymentGatewayConst::TYPEADDMONEY,
            'transfer_money'    => PaymentGatewayConst::TYPETRANSFERMONEY,
            'virtual_card'      => PaymentGatewayConst::VIRTUALCARD,
            'withdraw_money'    => PaymentGatewayConst::WITHDRAWMONEY,
            'add_sub_balance'   => PaymentGatewayConst::TYPEADDSUBTRACTBALANCE,


        ];
        $transaction_types = (object)$transaction_types;
        $data =[
            'transaction_types' => $transaction_types,
            'transactions'=> $transactions,
        ];
        $message =  ['success'=>[__("All Transactions")]];
        return Helpers::success($data,$message);
    }

}
