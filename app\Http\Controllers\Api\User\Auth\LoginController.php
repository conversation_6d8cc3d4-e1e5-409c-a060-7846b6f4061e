<?php

namespace App\Http\Controllers\Api\User\Auth;

use Exception;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserAuthorization;
use App\Traits\User\LoggedInUsers;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Traits\User\RegisteredUsers;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use App\Providers\Admin\BasicSettingsProvider;
use Illuminate\Validation\ValidationException;
use App\Http\Helpers\Api\Helpers as ApiHelpers;
use App\Notifications\User\Auth\SendAuthorizationCode;

class LoginController extends Controller
{
    use  LoggedInUsers, RegisteredUsers;
    protected $basic_settings;

    public function __construct()
    {
        $this->basic_settings = BasicSettingsProvider::get();
    }
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|max:50',
            'password' => !$request->has('is_social') ? 'required|min:6' : '',
            'provider_id' => $request->has('is_social') ? 'required|string' : '',
        ]);


        if ($validator->fails()) {
            $error = ['error' => $validator->errors()->all()];
            return ApiHelpers::validation($error);
        }

        if ($request->has('is_social')) {
            // Handle social login
            $user = User::where('email', strtolower(trim($request->email)))
                ->where('provider_id', $request->provider_id)
                ->first();

            if (!$user) {
                $error = ['error' => [__("User doesn't exist")]];
                return ApiHelpers::validation($error);
            }

            if (!$user->email_verified) {
                return response(['message' => __('Must Verify Email First')], 449);
            }

            if ($user->status == 0) {
                $error = ['error' => [__('Account Has been Suspended')]];
                return ApiHelpers::validation($error);
            }

            $this->refreshUserWallets($user);
            $this->createLoginLog($user);
            $token = $user->createToken('user_login')->accessToken;
            $data = ['token' => $token, 'user' => $user];
            $message = ['success' => [__('Login Successful')]];

            return ApiHelpers::success($data, $message);
        } else {
            // Handle regular login
            $user = User::where('username', trim(strtolower($request->email)))
                ->orWhere('email', trim(strtolower($request->email)))
                ->first();

            if (!$user) {
                $error = ['error' => [__("User doesn't exist")]];
                return ApiHelpers::validation($error);
            }

            if (!$user->email_verified) {
                return response(['message' => __('Must Verify Email First')], 449);
            }

            if (Hash::check($request->password, $user->password)) {
                if ($user->status == 0) {
                    $error = ['error' => [__('Account Has been Suspended')]];
                    return ApiHelpers::validation($error);
                }

                //  $this->refreshUserWallets($user);
                // $this->createLoginLog($user);
                $token = $user->createToken('user_login')->accessToken;
                $data = ['token' => $token, 'user' => $user];
                $message = ['success' => [__('Login Successful')]];
                return ApiHelpers::success($data, $message);
            } else {
                $error = ['error' => [__('Incorrect Password')]];
                return ApiHelpers::error($error);
            }
        }
    }


    public function register(Request $request)
    {
        $basic_settings = $this->basic_settings;
        $isSocial = $request->has('is_social') && $request->is_social == 1;

        // Default password rule
        $password_rule = "required|string|min:6";
        if ($basic_settings->secure_password && !$isSocial) {
            $password_rule = ["required", Password::min(8)->letters()->mixedCase()->numbers()->symbols()->uncompromised()];
        }

        // Agreement rule
        $agree = $basic_settings->agree_policy ? 'required' : '';

        // Validation rules
        $rules = [
            'firstname' => 'required|string|max:50',
            'lastname' => 'sometimes|string|max:50',
            'email' => 'required|email|max:160|unique:users,email',
            'device_id' => 'sometimes'
        ];

        if (!$isSocial) {
            $rules['password'] = $password_rule;
            $rules['agree'] = $agree;
        } else {
            $rules['provider_id'] = 'required';
            $rules['provider'] = 'required';
        }

        try {
            $validator = Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                $error = ['error' => $validator->errors()->all()];
                return ApiHelpers::validation($error);
            }

            $data = $request->all();

            // User creation
            $user = new User();
            $user->firstname = $data['firstname'] ?? null;
            $user->lastname = $data['lastname'] ?? null;
            $user->email = strtolower(trim($data['email']));

            if (!$isSocial) {
                $user->password = Hash::make($data['password']);
                $user->username = make_username($data['firstname'], $data['lastname'] ?? null);
            }

            $user->image = 'default.png';
            $user->address = [
                'address' => '',
                'state' => '',
                'zip' => '',
                'country' => '',
                'city' => ''
            ];

            // Set status and verification flags based on is_social
            if ($isSocial) {
                $user->status = 1;
                $user->email_verified = 1;
                $user->sms_verified = 1;
                $user->kyc_verified = 1;
                $user->provider_id = $request->provider_id;
                $user->username = make_username($data['firstname'], rand(100000, 999999));
                $user->provider = $request->provider;
                $user->is_social = $request->is_social;
                $user->email_verified_at = now();
            } else {
                $user->status = 1;
                $user->email_verified = $basic_settings->email_verification ? 0 : 1;
                $user->sms_verified = $basic_settings->sms_verification ? 0 : 1;
                $user->kyc_verified = $basic_settings->kyc_verification ? 0 : 1;
            }

            $user->save();

            // Skip email verification if is_social is true
            if (!$isSocial && $basic_settings->email_verification) {
                $emailError = null;
                $authData = [
                    'user_id' => $user->id,
                    'code' => generate_random_code(),
                    'token' => generate_unique_string("user_authorizations", "token", 200),
                    'created_at' => now(),
                ];

                DB::beginTransaction();
                try {
                    UserAuthorization::where("user_id", $user->id)->delete();
                    DB::table("user_authorizations")->insert($authData);
                    $user->notify(new SendAuthorizationCode((object)$authData));
                    DB::commit();
                } catch (Exception $e) {
                    logger($e);
                    DB::rollBack();
                    $emailError = __('There was an error in sending the email.');
                }
            }

            $this->createUserWallets($user);

            $message = ['success' => [__('Registration Successful')]];

            if (isset($emailError)) {
                $message['error'] = [$emailError];
            }

            return ApiHelpers::onlysuccess($message);
        } catch (ValidationException $e) {
            $error = ['error' => $e->errors()];
            return ApiHelpers::validation($error);
        } catch (\Exception $e) {
            logger($e);
            $error = ['error' => [__('An error occurred during registration. Please try again.')]];
            return ApiHelpers::error($error);
        }
    }


    public function logout()
    {
        Auth::user()->token()->revoke();
        $message = ['success' => [__('Logout Successful')]];
        return ApiHelpers::onlysuccess($message);
    }
}
