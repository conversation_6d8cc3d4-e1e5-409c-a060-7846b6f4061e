<?php

namespace App\Http\Controllers\Api\User;

use Exception;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\Transaction;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\VirtualUserCard;
use App\Models\UserNotification;
use App\Http\Helpers\Api\Helpers;
use App\Models\VirtualCardSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Constants\NotificationConst;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use App\Services\VirtualUserCardEpnService;

class EpnVirtualCardController extends Controller
{
    private $virtualCardService;
    public $successStatus      = 200;
    public $unauthorisedStatus = 401;

    public function __construct()
    {
        $this->virtualCardService = new VirtualUserCardEpnService();
    }

    public function getCardBins(Request $request)
    {
        try {
            $cardBins = $this->virtualCardService->getAllCardPins();

            if (isset($cardBins) && count($cardBins) > 0) {
                // Get the forbidden cards from the settings
                $forbidCards = explode(",", trim($this->virtualCardService->virtual_card_setting->forbidden_cards));

                // Filter out the forbidden card bins
                $cardBins = $cardBins->filter(function ($el) use ($forbidCards) {
                    return !in_array($el["bin"], $forbidCards);
                })->values();

                $filterType = $request->query('type');
                $filteredCardBins = $cardBins->when($filterType, function ($collection) use ($filterType) {
                    return $collection->filter(function ($el) use ($filterType) {
                        return $el["type"] == $filterType;
                    });
                })->values();
            } else {
                $filteredCardBins = collect();
            }
            return Helpers::success($filteredCardBins, __('success'));
        } catch (\Exception $e) {
            Log::error('Error fetching card bins: ' . $e->getMessage(), [
                'exception' => $e,
                'request' => $request->all()
            ]);
            return Helpers::success(null, $e->getMessage());
        }
    }

    public function index()
    {
        try {
            // Check if cards are not loaded yet
            $unloadedCards = VirtualUserCard::where(['user_id' => auth()->id()])
                ->where([
                    'status' => VirtualUserCard::$Inactive,
                    "cardId" => null,
                ])
                ->where("external_id", "!=", null)
                ->get();
            if (count($unloadedCards) > 0) {

                $this->virtualCardService->getUserUnloadedCardsAfterCreation($unloadedCards);
            }

            // Get the active cards
            $cards = VirtualUserCard::where(['user_id' => auth()->id()])
                ->where('status', VirtualUserCard::$Active)
                ->orderBy("id", "desc")
                ->select('cardId', 'cvv', 'balance', 'maskedNumber', 'cardName', 'account_uuid', 'holder_address', 'expiration', 'status', 'request_to_block', 'is_default')
                ->get();

            // Define the card bins to compare
            $cardBins = [
                ['type' => 'MasterCard', 'bin' => 5],
                ['type' => 'Visa', 'bin' => 4],
            ];


            // Function to get the card type by bin
            function getCardTypeByBin($cardBins, $cardBin)
            {
                foreach ($cardBins as $binInfo) {
                    if (strpos((string)$cardBin, (string)$binInfo['bin']) === 0) {
                        return $binInfo['type'];
                    }
                }
                return 'Unknown';
            }

            // Add the card type to each card
            foreach ($cards as $card) {
                // Extract the BIN from the masked number
                $maskedNumber = $card->maskedNumber; // assuming the masked number contains the bin
                $cardBin = substr($maskedNumber, 0, 6); // assuming BIN is the first 6 digits
                $card->type = getCardTypeByBin($cardBins, $cardBin);
            }

            $settings = VirtualCardSetting::latest()->first();
            $allow_adv_cards = $this->virtualCardService->virtual_card_setting->allow_adv_cards;
            return Helpers::success($cards, __('success'));
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => __('something went wrong')], 500);
        }
    }

    public function rechargeinfo()
    {
        $user = auth()->user();
        $wallet = UserWallet::where('user_id', $user->id)->first();

        // Check if wallet exists
        if (!$wallet) {
            return Helpers::error(null, __('Wallet not found'));
        }

        // Fetch the setting with additional card_min_start_balance
        $setting = VirtualCardSetting::select(
            'max_purchase_limit',
            'min_purchase_limit',
            'percent_recharge_fee',
            'fixed_recharge_fee',
            'percent_creation_fee',
            'fixed_creation_fee',
            'card_min_start_balance' // Add this column to the select
        )->first();

        // Check if setting exists
        if (!$setting) {
            return Helpers::error(null, __('Settings not found'));
        }

        // Compare min_purchase_limit and card_min_start_balance, and get the larger value
        $minPurchaseLimit = max($setting->min_purchase_limit, $setting->card_min_start_balance);

        return Helpers::success([
            'wallet' => [
                'balance' => $wallet->balance,
                'currency' => $wallet->currency->code,
            ],
            'setting' => [
                'max_purchase_limit' => $setting->max_purchase_limit,
                'min_purchase_limit' => $minPurchaseLimit, // Return the larger value
                'percent_recharge_fee' => $setting->percent_recharge_fee,
                'fixed_recharge_fee' => $setting->fixed_recharge_fee,
                'percent_creation_fee' => $setting->percent_creation_fee,
                'fixed_creation_fee' => $setting->fixed_creation_fee,
            ]
        ], __('success'));
    }

    public function getCardTransactions($account_uuid)
    {
        $response =  Http::withHeaders([
            'Accept' => 'application/json',
            'Content-type' => 'application/json',
            'accept' => 'application/json',
            'authorization' => "Bearer {$this->virtualCardService->virtual_card_setting->epn_token}",
        ])->post(
            $this->virtualCardService->enp_api_url . "/transaction?account_uuid={$account_uuid}"

        );
        
        $res = $response->json();
        $result = [];
        if ($response->status() == 200) {
            $data = $res['data'];
            for ($i = 0; $i < count($data); $i++) {
                $single_transaction = [];
                $single_transaction['id'] = $data[$i]['id'];
                $single_transaction['amount'] = $data[$i]['amount'];
                $single_transaction['payable'] = $data[$i]['amount_full'];
                $single_transaction['description'] = $data[$i]['description'];
                $single_transaction['transaction_type'] = $data[$i]['type_description'];
                $single_transaction['status'] =
                    $data[$i]['status'] == 30 ? "Success" : "Declined";
                $single_transaction['created_at'] = $data[$i]['created_at'];
                $single_transaction['processed_at'] = $data[$i]['processed_at'];

                array_push($result, $single_transaction);
            }

            return response()->json(['success' => true, 'data' => $result]);
        } else {
            return response()->json(['success' => false, 'message' => 'something went wrong'], 500);
        }
    }

    public function rechargeCard(Request $request)
    {
        $this->validate($request, [
            'amount' => "required|numeric|min:0",
            'account_uuid' => "required",
            'cardId' => 'required'
        ]);
        $data = [];
        //check balance
        $balance = UserWallet::where(['user_id' => auth()->id(), 'currency_id' => 1])->first();
        $fees = ($request->amount * ($this->virtualCardService->virtual_card_setting->percent_recharge_fee / 100)) + $this->virtualCardService->virtual_card_setting->fixed_recharge_fee;

        if ($balance->balance < $fees + $request->amount) {
            return $this->ApiResponse('401', __('fail'), '', __("No enough balance"));
        }

        $accountBalance = $this->getAccountBalance();
        // if ($request->amount > $accountBalance) {
        //     (new EmailController())->sendEmail("<EMAIL>", "Virtual Visa Cards", __("Sorry, the balance of the virtual visa cards is not enough to complete the purchase. Please recharge."));
        //     return $this->ApiResponse('200', __('success'), '', __("Service is not available now, please try later"));
        // }
        try {

            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-type' => 'application/json',
                'authorization' => "Bearer {$this->virtualCardService->virtual_card_setting->epn_token}",
            ])->post(
                $this->virtualCardService->enp_api_url . "/transfer/transfer",
                [
                    "from_account_uuid" => $this->virtualCardService->virtual_card_setting->account_uuid,
                    "to_account_uuid" => $request->account_uuid,
                    "amount" => $request->amount,
                ]
            );

            $res = $response->json();
            if ($response->status() == 200) {
                // Increase card balance in db


                // Update the latest balance of visa
                $card = VirtualUserCard::where(['user_id' => auth()->id(), 'account_uuid' => $request->account_uuid, 'cardId' => $request->cardId])->first();
                if (!$card) {
                    return $this->ApiResponse('200', __('fail'), '', __("card not found"));
                }
                $accountDetails = $this->virtualCardService->getCardDetailsAfterCreation($card);

                VirtualUserCard::where(['user_id' => auth()->id(), 'account_uuid' => $request->account_uuid, 'cardId' => $request->cardId])
                    ->increment('balance', $request->amount);

                $card_id = VirtualUserCard::where(['user_id' => auth()->id(), 'account_uuid' => $request->account_uuid, 'cardId' => $request->cardId])->value("id");
                $transaction_id_response = $this->virtualCardService->rechargeVirtualCardTransaction($request, $request->cardId);

                // Decrease from wallet balance
                $wallet_balance_before = $balance->balance;
                $balance->balance = $wallet_balance_before - ($fees + $request->amount);
                $balance->save();
                $wallet_balance_after = $balance->balance;

                // $this->virtualCardService->createBalanceObserver($transaction_id_response, $wallet_balance_before, $wallet_balance_after, 29, auth()->id(), 29, $card->id);

                return Helpers::onlysuccess(__("Card balance credited successfully"));
            } else {
                if (
                    str_contains($res["message"], 'Not enough money') ||
                    str_contains($res["message"], 'Low balance')
                ) {
                    return Helpers::error(null, __('service is not availble now, please try later'));
                }
                return Helpers::error(null, __("Something went wrong"));
            }
        } catch (\Exception $e) {
            return Helpers::error(null, __("Something went wrong"));
        }
    }

    private function getAccountBalance()
    {
        try {
            //59abdd68-77cf-472e-aac1-2fcc05b8f093
            $response =  Http::withHeaders([
                'Accept' => 'application/json',
                'Content-type' => 'application/json',
                'accept' => 'application/json',
                'X-CSRF-TOKEN' => '',
                'authorization' => "Bearer {$this->virtualCardService->virtual_card_setting->epn_token}",
            ])->get(
                $this->virtualCardService->enp_api_url . "/account/{$this->virtualCardService->virtual_card_setting->account_uuid}"
            );

            $res = $response->json();
            if ($response->status() == 200 && $res["data"] != null) {
                $balance = $res["data"]["balance"];
                return $balance;
            } else {
                return null;
            }
        } catch (Exception $e) {
            return null;
        }
    }

    public function showCard($cardId)
    {
        try {
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-type' => 'application/json',
                'accept' => 'application/json',
                'authorization' => "Bearer {$this->virtualCardService->virtual_card_setting->epn_token}",
            ])->get(
                $this->virtualCardService->enp_api_url . "/card/$cardId/showpan"
            );
            $res = $response->json();
            $cardBins = [
                ['type' => 'MasterCard', 'bin' => 5],
                ['type' => 'Visa', 'bin' => 4],
            ];

            // Function to get the card type by bin
            function getCardTypeByBin($cardBins, $cardBin)
            {
                foreach ($cardBins as $binInfo) {
                    if (strpos((string)$cardBin, (string)$binInfo['bin']) === 0) {
                        return $binInfo['type'];
                    }
                }
                return 'Unknown';
            }

            $result = [];
            if ($response->status() == 200) {
                $cardInDb = VirtualUserCard::where(['user_id' => auth()->id(), 'cardId' => $cardId])->first();
                $balance = $this->virtualCardService->getCardDetailsAfterCreation($cardInDb);
                // Get card balance
                $data = $res['data'];
                $result['cardNumber'] = $data['number'];
                $result['cvv'] = $data['cvx2'];
                $result['maskedNumber'] = $cardInDb->maskedNumber;
                $result['expiration'] = $cardInDb->expiration;
                $result['cardType'] = $data['number'][0] == '4' ? 'Visa' : ($data['number'][0] == '5' ? 'Mastercard' : 'Unknown');
                $result['cardName'] = $cardInDb->cardName;
                $result['expiration'] = $cardInDb->expiration;


                $result['account_uuid'] = $cardInDb->account_uuid;
                $result['cardId'] = $cardInDb->cardId;
                $result['is_blocked'] = $cardInDb->request_to_block;
                $result['is_default'] = $cardInDb->is_default;
                $result['currency'] = 'USD';


                $result['balance'] = $balance['account']['balance'] ?? $cardInDb->balance;

                // Check if holder_address exists before accessing it
                if (isset($balance['holder_address'])) {
                    $result['holder_address'] = $balance['holder_address'];
                } else {
                    $result['holder_address'] = $cardInDb->holder_address; // or handle the missing value as needed
                }

                return Helpers::success($result, __('success'));
            } else {
                return Helpers::error(null, __('service is not availble now, please try later'));
            }
        } catch (\Exception $e) {
            Log::error('Error previewing card details: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
            return Helpers::error(null, __('Something went wrong'));
        }
    }

    public function addNewCard(Request $request)
    {
        try {
            $validation = Validator::make($request->all(), [
                "amount" => "required|numeric",
                "name" => "required|string",
                "bin" => "required|numeric",

            ]);

            if ($validation->fails()) {
                return response()->json(['data' => [
                    'errors' => $validation->errors(),
                ]], 422);
            }
            $data = [];
            $reloadAfterCreate = false;
            $cardName = $request->name ?? "Lirat User";
            $external_id = Str::uuid();
            $binArr = explode("|", $request->bin);
            if (!isset($this->virtualCardService->virtual_card_setting)) {
                return response()->json([
                    'success' => false,
                    'error' => "unavailable",
                    'message' => __('unavailable')
                ]);
            }

            if ($request->amount <= 0) {
                return response()->json([
                    'success' => false,
                    'error' => "unavailable",
                    'message' => __("amount is not valid")
                ]);
            }

            // Check user card count
            $user_cards_count = VirtualUserCard::where(['user_id' => auth()->user()->id])->count();
            if (isset($this->virtualCardService->virtual_card_setting->max_limit) && $user_cards_count >= $this->virtualCardService->virtual_card_setting->max_limit) {
                return response()->json([
                    'success' => false,
                    'error' => "exceeded_maximum_card_count",
                    'message' => __('exceeded maximum card count limit')
                ]);
            }

            // Check balance
            $wallet = UserWallet::where(['user_id' => auth()->user()->id, 'currency_id' => 1])->first();
            $fees = ($request->amount * ($this->virtualCardService->virtual_card_setting->percent_creation_fee / 100)) + $this->virtualCardService->virtual_card_setting->fixed_creation_fee;

            if ($wallet->balance < $fees + $request->amount) {
                return response()->json([
                    'success' => false,
                    'error' => "no_enough_balance",
                    'message' => __('no enough balance')
                ], 400);
            }

            $accountBalance = $this->virtualCardService->getAccountBalance();
            if ($request->amount > $accountBalance) {
                // (new EmailController())->sendEmail("<EMAIL>", "بطاقات الفيزا الإفتراضية", "عفوا رصيد بطاقات الفيزا الإفتراضية اصبح غير كافى لإتمام عمليات الشراء الرجاء إعادة الشحن");
                return response()->json([
                    'success' => false,
                    'error' => "unavailable",
                    'message' => __('service is not availble now, please try later')
                ]);
            }

            //make request
            $response =  Http::withHeaders([
                'Accept' => 'application/json',
                'Content-type' => 'application/json',
                'accept' => 'application/json',
                'authorization' => "Bearer {$this->virtualCardService->virtual_card_setting->epn_token}",
            ])->post(
                $this->virtualCardService->enp_api_url . "/card/buy",
                [
                    "account_uuid" => $this->virtualCardService->virtual_card_setting->account_uuid,
                    "start_balance" => $request->amount,
                    "description" => $cardName,
                    "bin" =>  $binArr[0], //5371001 ==> mastercard //4865551 ==> visa ********
                    "external_id" => $external_id,
                    "cards_count" => 1,
                    "auto_replenishment" => 0
                ]
            );
            $res = $response->json();

            if ($response->status() == 200 && $res["success"] == true) {
                //create model of card
                $new_virtual_card = VirtualUserCard::create([
                    'user_id' => auth()->id(),
                    'external_id' => $external_id,
                    'cardName' => $cardName,
                    'balance' => $request->amount,
                    'platform' => $binArr[0],
                    'status' => VirtualUserCard::$Inactive
                ]);
                $totalFees = ($request->amount * ($this->virtualCardService->virtual_card_setting->percent_creation_fee / 100)) + $this->virtualCardService->virtual_card_setting->fixed_creation_fee;

                $transaction_id_response =  $this->virtualCardService->createVirtualCardTransaction($request, $external_id);
                $this->insertBuyCardCharge($this->virtualCardService->virtual_card_setting->fixed_creation_fee, $this->virtualCardService->virtual_card_setting->percent_creation_fee, $totalFees, auth()->user(), $transaction_id_response, $request->amount);

                $wallet_balance_before = $wallet->balance;
                $wallet->balance = $wallet->balance - ($fees + $request->amount);
                $wallet->save();

                $wallet_balance_after = $wallet->balance;
                $reloadAfterCreate = true;



                return response()->json([
                    'success' => true,
                    "reloadAfterCreate" => $reloadAfterCreate,
                    'message' => __("card is requested successfully please wait one or two minute until fully created!")
                ]);
            } else {
                if (
                    str_contains($res["message"], 'Not enough money') ||
                    str_contains($res["message"], 'enough money') ||
                    str_contains($res["message"], 'Low balance') ||
                    str_contains($res["message"], 'balance') ||
                    str_contains($res["message"], 'enough') ||
                    str_contains($res["message"], 'Something went wrong')

                )
                    return response()->json([
                        'success' => false,
                        'error' => "unavailable",
                        'message' => __('something went wrong')
                    ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('something went wrong')
            ]);
        }
    }

    public function insertBuyCardCharge($fixedCharge, $percent_charge, $total_charge, $user, $id, $masked_card)
    {
        DB::beginTransaction();
        try {
            DB::table('transaction_charges')->insert([
                'transaction_id'    => $id,
                'percent_charge'    => $percent_charge,
                'fixed_charge'      => $fixedCharge,
                'total_charge'      => $total_charge,
                'created_at'        => now(),
            ]);
            DB::commit();

            //notification
            $notification_content = [
                'title'         => "Buy Card",
                'message'       => 'Buy card successful ' . $masked_card,
                'image'         => files_asset_path('profile-default'),
            ];

            UserNotification::create([
                'type'      => NotificationConst::CARD_BUY,
                'user_id'  => $user->id,
                'message'   => $notification_content,
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }

    public function blockCard($cardId)
    {
        //check if the user is the owner
        $card = VirtualUserCard::where(['user_id' => auth()->id(), 'cardId' => $cardId])->first();
        if (isset($card)) {
            $balance = $this->virtualCardService->getCardDetailsAfterCreation($card);
            if ($card->status = VirtualUserCard::$Active) {
                $response =  Http::withHeaders([
                    'Accept' => 'application/json',
                    'Content-type' => 'application/json',
                    'accept' => 'application/json',
                    'authorization' => "Bearer {$this->virtualCardService->virtual_card_setting->epn_token}",
                ])->delete(
                    $this->virtualCardService->enp_api_url . "/card",
                    [
                        "card_uuids" => [$cardId]
                    ]
                );
                $res = $response->json();
                if ($response->status() == 200) {
                    VirtualUserCard::where(['cardId' => $cardId])
                        ->update(['status' => VirtualUserCard::$Inactive]);
                    $card->request_to_block = 1;
                    $card->save();

                    $wallet = UserWallet::where(["user_id" => $card->user_id, "currency_id" => 1])->first();

                    $wallet->balance += $balance;
                    $wallet->save();

                    return Helpers::onlysuccess(__('Deleted success'));
                } else {
                    return response()->json(['success' => false]);
                }
                return response()->json(['success' => false]);
            }
        } else {
            return response()->json(['success' => false, 'message' => __('Not Allowed')]);
        }
    }

    public function makeDefaultOrRemove(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cardId'     => "required|string",
        ]);
        if ($validator->fails()) {
            $error =  ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }
        $validated = $validator->validate();
        $user = auth()->user();
        $targetCard =  VirtualUserCard::where('cardId', $validated['cardId'])->where('user_id', $user->id)->first();
        if (!$targetCard) {
            $error = ['error' => [__('Something Is Wrong In Your Card')]];
            return Helpers::error($error);
        };
        $withOutTargetCards =  VirtualUserCard::where('id', '!=', $targetCard->id)->where('user_id', $user->id)->get();
        try {
            $targetCard->update([
                'is_default'         => $targetCard->is_default ? 0 : 1,
            ]);
            if (isset($withOutTargetCards)) {
                foreach ($withOutTargetCards as $card) {
                    $card->is_default = false;
                    $card->save();
                }
            }
            $message =  ['success' => [__('Status Updated Successfully')]];
            return Helpers::onlysuccess($message);
        } catch (Exception $e) {
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }

    public function changeStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cardId'     => "required|string",
        ]);
        if ($validator->fails()) {
            $error =  ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }
        $validated = $validator->validate();
        $user = auth()->user();
        $targetCard =  VirtualUserCard::where('cardId', $validated['cardId'])->where('user_id', $user->id)->first();
        if (!$targetCard) {
            $error = ['error' => [__('Something Is Wrong In Your Card')]];
            return Helpers::error($error);
        };
        try {
            $targetCard->update([
                'request_to_block'         => $targetCard->status ? 0 : 1,
            ]);

            $targetCard->save();


            $message =  ['success' => [__('Status Updated Successfully')]];
            return Helpers::onlysuccess($message);
        } catch (Exception $e) {
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }
}
