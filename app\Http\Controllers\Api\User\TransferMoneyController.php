<?php

namespace App\Http\Controllers\Api\User;

use App\Constants\NotificationConst;
use App\Constants\PaymentGatewayConst;
use App\Http\Controllers\Controller;
use App\Http\Helpers\Api\Helpers;
use App\Models\Admin\BasicSettings;
use App\Models\Admin\Currency;
use App\Models\Admin\TransactionSetting;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserNotification;
use App\Models\UserWallet;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TransferMoneyController extends Controller
{
    protected  $trx_id;
    public function __construct()
    {
        $this->trx_id = 'SM' . getTrxNum();
    }
    public function transferMoneyInfo()
    {
        $user = auth()->user();
        $sendMoneyCharge = TransactionSetting::where('slug', 'transfer-money')->where('status', 1)->get()->map(function ($data) {
            return [
                'id' => $data->id,
                'slug' => $data->slug,
                'title' => $data->title,
                'fixed_charge' => getAmount($data->fixed_charge, 2),
                'percent_charge' => getAmount($data->percent_charge, 2),
                'min_limit' => getAmount($data->min_limit, 2),
                'max_limit' => getAmount($data->max_limit, 2),
                'monthly_limit' => getAmount($data->monthly_limit, 2),
                'daily_limit' => getAmount($data->daily_limit, 2),
            ];
        })->first();
        $transactions = Transaction::auth()->transferMoney()->latest()->take(10)->get()->map(function ($item) {
            $statusInfo = [
                "success" =>      1,
                "pending" =>      2,
                "rejected" =>     3,
            ];
            if ($item->attribute == payment_gateway_const()::SEND) {
                return [
                    'id' => @$item->id,
                    'type' => $item->attribute,
                    'trx' => @$item->trx_id,
                    'transaction_type' => $item->type,
                    'transaction_heading' => __("Transfer Money to") . " " . @$item->details->receiver->firstname . " " . @$item->details->receiver->lastname,
                    'request_amount' => getAmount(@$item->request_amount, 2) . ' ' . get_default_currency_code(),
                    'total_charge' => getAmount(@$item->charge->total_charge, 2) . ' ' . get_default_currency_code(),
                    'payable' => getAmount(@$item->payable, 2) . ' ' . get_default_currency_code(),
                    'recipient_received' => getAmount(@$item->details->recipient_amount, 2) . ' ' . get_default_currency_code(),
                    'current_balance' => getAmount(@$item->available_balance, 2) . ' ' . get_default_currency_code(),
                    'status' => @$item->stringStatus->value,
                    'date_time' => @$item->created_at,
                    'status_info' => (object)@$statusInfo,
                ];
            } elseif ($item->attribute == payment_gateway_const()::RECEIVED) {
                return [
                    'id' => @$item->id,
                    'type' => $item->attribute,
                    'trx' => @$item->trx_id,
                    'transaction_type' => $item->type,
                    'transaction_heading' => __("Transfer Money from") . " " . @$item->details->sender->firstname . " " . @$item->details->sender->lastname,
                    'request_amount' => getAmount(@$item->request_amount, 2) . ' ' . get_default_currency_code(),
                    'total_charge' => getAmount(@$item->charge->total_charge, 2) . ' ' . get_default_currency_code(),
                    'payable' => getAmount(@$item->payable, 2) . ' ' . get_default_currency_code(),
                    'recipient_received' => getAmount(@$item->details->recipient_amount, 2) . ' ' . get_default_currency_code(),
                    'current_balance' => getAmount(@$item->available_balance, 2) . ' ' . get_default_currency_code(),
                    'status' => @$item->stringStatus->value,
                    'date_time' => @$item->created_at,
                    'status_info' => (object)@$statusInfo,
                ];
            }
        });

        $userWallet = UserWallet::where('user_id', $user->id)->get()->map(function ($data) {
            return [
                'balance' => getAmount($data->balance, 2),
                'currency' => get_default_currency_code(),
            ];
        })->first();
        $data = [
            'base_curr' => get_default_currency_code(),
            'base_curr_rate' => get_default_currency_rate(),
            'transferMoneyCharge' => (object)$sendMoneyCharge,
            'userWallet' =>  (object)$userWallet,
            'transactions'   => $transactions,
        ];
        $message =  ['success' => [__('Transfer Money Information')]];
        return Helpers::success($data, $message);
    }

    public function checkUser(Request $request)
    {
        $validator = Validator::make(request()->all(), [
            'email'     => "required|email",
        ]);
        if ($validator->fails()) {
            $error =  ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }
        $column = "email";
        if (check_email($request->email)) $column = "email";
        $exist = User::where($column, $request->email)->first();
        if (!$exist) {
            $error = ['error' => [__('User not found')]];
            return Helpers::error($error);
        }
        $user = auth()->user();
        if (@$exist && $user->email == @$exist->email) {
            $error = ['error' => [__("Can\'t transfer money to your own")]];
            return Helpers::error($error);
        }
        $data = [
            'exist_user'   => $exist,
        ];
        $message =  ['success' => [__("Valid user for transfer money")]];
        return Helpers::success($data, $message);
    }

    public function confirmedTransferMoney(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|gt:0',
            'email'  => 'required|email'
        ]);

        if ($validator->fails()) {
            return Helpers::validation('failed', $validator->errors());
        }

        $user = auth()->user();
        $amount = $request->amount;

        $sendMoneyCharge = TransactionSetting::where('slug', 'transfer-money')
            ->where('status', 1)
            ->first();

        $userWallet = UserWallet::where('user_id', $user->id)->first();
        if (!$userWallet) {
            return Helpers::error(['error' => [__("Sender wallet not found")]]);
        }

        $baseCurrency = Currency::default();
        if (!$baseCurrency) {
            return Helpers::error(['error' => [__('Default Currency Not Setup Yet')]]);
        }
        $rate = $baseCurrency->rate;

        $receiver = User::where('email', $request->email)->first();
        if (!$receiver) {
            return Helpers::error(['error' => [__("Receiver not exist")]]);
        }
        if ($receiver->email == $user->email) {
            return Helpers::error(['error' => [__("Can't transfer money to yourself")]]);
        }

        $receiverWallet = UserWallet::where('user_id', $receiver->id)->first();
        if (!$receiverWallet) {
            return Helpers::error(['error' => [__("Receiver wallet not found")]]);
        }

        $minLimit = $sendMoneyCharge->min_limit * $rate;
        $maxLimit = $sendMoneyCharge->max_limit * $rate;
        if ($amount < $minLimit || $amount > $maxLimit) {
            return Helpers::error(['error' => [__('Please follow the transaction limit')]]);
        }

        // --- Fee calculation ---
        $fixedCharge   = $sendMoneyCharge->fixed_charge * $rate;
        $percentCharge = ($amount / 100) * $sendMoneyCharge->percent_charge;
        $totalCharge   = $fixedCharge + $percentCharge;

        // Sender pays exactly what they entered
        $payable = $amount;

        // Receiver gets amount minus fees
        $recipient = $amount - $totalCharge;

        if ($recipient <= 0) {
            return Helpers::error(['error' => [__('Amount too low after fees deducted')]]);
        }

        if ($payable > $userWallet->balance) {
            return Helpers::error(['error' => [__('Sorry, insufficient balance')]]);
        }

        try {
            $trx_id = $this->trx_id;

            // Sender transaction
            $sender = $this->insertSender($trx_id, $user, $userWallet, $amount, $recipient, $payable, $receiver);
            if ($sender) {
                $this->insertSenderCharges($fixedCharge, $percentCharge, $totalCharge, $amount, $user, $sender, $receiver);
            }

            // Receiver transaction
            $receiverTrans = $this->insertReceiver($trx_id, $user, $userWallet, $amount, $recipient, $payable, $receiver, $receiverWallet);
            if ($receiverTrans) {
                $this->insertReceiverCharges($fixedCharge, $percentCharge, $totalCharge, $amount, $user, $receiverTrans, $receiver);
            }

            return Helpers::onlysuccess([
                'success' => [__('Transfer Money successful to') . ' ' . $receiver->fullname]
            ]);
        } catch (Exception $e) {
            return Helpers::error(['error' => [__("Something Went Wrong! Please Try Again")]]);
        }
    }

    // --- Sender Transaction ---
    public function insertSender($trx_id, $user, $userWallet, $amount, $recipient, $payable, $receiver)
    {
        $afterBalance = $userWallet->balance - $payable;

        $details = [
            'recipient_amount' => $recipient,
            'receiver'         => $receiver,
        ];

        DB::beginTransaction();
        try {
            $id = DB::table("transactions")->insertGetId([
                'user_id'            => $user->id,
                'user_wallet_id'     => $userWallet->id,
                'payment_gateway_currency_id' => null,
                'type'               => PaymentGatewayConst::TYPETRANSFERMONEY,
                'trx_id'             => $trx_id,
                'request_amount'     => $amount,
                'payable'            => $payable, // sender paid (same as entered amount)
                'available_balance'  => $afterBalance,
                'remark'             => "Transfer To " . $receiver->fullname,
                'details'            => json_encode($details),
                'attribute'          => PaymentGatewayConst::SEND,
                'status'             => true,
                'created_at'         => now(),
            ]);

            $this->updateSenderWalletBalance($userWallet, $afterBalance);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return Helpers::error(['error' => [__("Something Went Wrong! Please Try Again")]]);
        }

        return $id;
    }

    public function updateSenderWalletBalance($authWalle, $afterCharge)
    {
        $authWalle->update([
            'balance'   => $afterCharge,
        ]);
    }
    public function insertSenderCharges($fixedCharge, $percent_charge, $total_charge, $amount, $user, $id, $receiver)
    {
        DB::beginTransaction();
        try {
            DB::table('transaction_charges')->insert([
                'transaction_id'    => $id,
                'percent_charge'    => $percent_charge,
                'fixed_charge'      => $fixedCharge,
                'total_charge'      => $total_charge,
                'created_at'        => now(),
            ]);
            DB::commit();

            //notification
            $notification_content = [
                'title'         => "transfer Money",
                'message'       => "Transfer Money to" . " " . $receiver->fullname . ' ' . $amount . ' ' . get_default_currency_code() . " successful",
                'image'         => files_asset_path('profile-default'),
            ];

            UserNotification::create([
                'type'      => NotificationConst::TRANSFER_MONEY,
                'user_id'  => $user->id,
                'message'   => $notification_content,
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }

    // --- Receiver Transaction ---
    public function insertReceiver($trx_id, $user, $userWallet, $amount, $recipient, $payable, $receiver, $receiverWallet)
    {
        $recipientBalance = $receiverWallet->balance + $recipient;

        $details = [
            'sender_amount' => $amount,
            'sender'        => $user,
        ];

        DB::beginTransaction();
        try {
            $id = DB::table("transactions")->insertGetId([
                'user_id'            => $receiver->id,
                'user_wallet_id'     => $receiverWallet->id,
                'payment_gateway_currency_id' => null,
                'type'               => PaymentGatewayConst::TYPETRANSFERMONEY,
                'trx_id'             => $trx_id,
                'request_amount'     => $amount,     // original sent amount
                'payable'            => $recipient,  // what they actually receive
                'available_balance'  => $recipientBalance,
                'remark'             => "Transfer From " . $user->fullname,
                'details'            => json_encode($details),
                'attribute'          => PaymentGatewayConst::RECEIVED,
                'status'             => true,
                'created_at'         => now(),
            ]);

            $this->updateReceiverWalletBalance($receiverWallet, $recipientBalance);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return Helpers::error(['error' => [__("Something Went Wrong! Please Try Again")]]);
        }

        return $id;
    }

    public function updateReceiverWalletBalance($receiverWallet, $recipient_amount)
    {
        $receiverWallet->update([
            'balance'   => $recipient_amount,
        ]);
    }
    public function insertReceiverCharges($fixedCharge, $percent_charge, $total_charge, $amount, $user, $id, $receiver)
    {
        DB::beginTransaction();
        try {
            DB::table('transaction_charges')->insert([
                'transaction_id'    => $id,
                'percent_charge'    => $percent_charge,
                'fixed_charge'      => $fixedCharge,
                'total_charge'      => $total_charge,
                'created_at'        => now(),
            ]);
            DB::commit();

            //notification
            $notification_content = [
                'title'         => __("transfer Money"),
                'message'       => __("Transfer Money from") . " " . $user->fullname . ' ' . $amount . ' ' . get_default_currency_code() . " successful",
                'image'         => files_asset_path('profile-default'),
            ];

            UserNotification::create([
                'type'      => NotificationConst::TRANSFER_MONEY,
                'user_id'  => $receiver->id,
                'message'   => $notification_content,
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }
}
