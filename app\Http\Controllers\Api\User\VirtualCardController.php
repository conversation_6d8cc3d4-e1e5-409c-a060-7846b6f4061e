<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Helpers\Api\Helpers;
use App\Http\Resources\CardResource;
use App\Models\UserWallet;
use App\Models\VirtualCardSetting;
use App\Models\VirtualUserCard;
use App\Services\LiratVirtualCardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class VirtualCardController extends Controller
{
    private $virtualCardService;
    private $api_settings;

    public function __construct()
    {
        $this->virtualCardService = new LiratVirtualCardService();
        $this->api_settings = VirtualCardSetting::latest('id')->first();
    }

    public function info()
    {
        return Helpers::success([
            'min_amount' => (string) $this->api_settings->min_amount,
            'max_amount' => (string) $this->api_settings->max_amount,
            'fixed_creation_fee' => (string) $this->api_settings->fixed_creation_fee,
            'percent_creation_fee' => (string) $this->api_settings->percent_creation_fee,
            'fixed_recharge_fee' => (string) $this->api_settings->fixed_recharge_fee,
            'percent_recharge_fee' => (string) $this->api_settings->percent_recharge_fee
        ]);
    }

    public function index()
    {
        $cards = VirtualUserCard::where('user_id', auth()->id())
            ->where('is_terminated', 0)
            ->get();

        return response()->json([
            'success' => true,
            'data' => CardResource::collection($cards)
        ]);
    }

    public function store(Request $request)
    {
        $data = $request->all();

        // 1. Validate input
        $validator = Validator::make($data, [
            'amount' => [
                'required',
                'numeric',
                'min:' . $this->api_settings->min_amount,
                'max:' . $this->api_settings->max_amount,
            ],
            'name' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()
            ];
        }

        $user_cards_count = VirtualUserCard::where(['user_id' => auth()->user()->id])->count();
        if (isset($this->api_settings->max_limit) && $user_cards_count >= $this->api_settings->max_limit) {
            return response()->json([
                'success' => false,
                'error' => "exceeded_maximum_card_count",
                'message' => __('exceeded maximum card count limit')
            ]);
        }

        // Check balance
        $wallet = UserWallet::firstOrCreate(['user_id' => auth()->id(), 'currency_id' => 1]);
        $percentageFees = request('amount') * ($this->api_settings->percent_creation_fee / 100);
        $fees = $percentageFees + $this->api_settings->fixed_creation_fee;

        if ($wallet->balance < request('amount')) {
            return response()->json([
                'success' => false,
                'error' => "no_enough_balance",
                'message' => __('no enough balance')
            ], 400);
        }

        $data['fees'] = $fees;
        $data['actualAmount'] = $data['amount'] - $data['fees'];

        try {
            DB::transaction(function () use ($data, $wallet, $percentageFees) {
                $card = $this->virtualCardService->createCard($data);

                $transaction_id_response =  $this->virtualCardService->createVirtualCardTransaction($data, $card->external_id);

                $this->virtualCardService->insertBuyCardCharge(
                    $this->api_settings->fixed_creation_fee,
                    $percentageFees,
                    $data['fees'],
                    auth()->user(),
                    $transaction_id_response,
                    $card->maskedNumber
                );

                $wallet->balance = $wallet->balance - $data['amount'];
                $wallet->save();
            });

            return response()->json([
                'success' => true,
                "reloadAfterCreate" => true,
                'message' => __("card is requested successfully please wait one or two minute until fully created!")
            ]);
        } catch (\Throwable $th) {
            Log::error('Error creating card: ' . $th->getMessage(), [
                'exception' => $th
            ]);

            return response()->json([
                'success' => false,
                'error' => "unavailable",
                'message' => __('service is not availble now, please try later')
            ]);
        }
    }

    public function show(string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)
            ->where('user_id', auth()->id())
            ->where('is_terminated', 0)
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => CardResource::make($card)
        ]);
    }

    public function recharge(Request $request, string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)
            ->where('user_id', auth()->id())
            ->where('is_terminated', 0)
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        // 1. Validate input
        $validator = Validator::make($request->all(), [
            'amount' => [
                'required',
                'numeric',
                'min:' . $this->api_settings->min_amount,
                'max:' . $this->api_settings->max_amount,
            ],
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()
            ];
        }

        // 2. Check wallet balance
        $wallet = UserWallet::firstOrCreate(['user_id' => auth()->id(), 'currency_id' => 1]);

        $percentageFees = $request->amount * ($this->api_settings->percent_recharge_fee / 100);
        $fees = $percentageFees + $this->api_settings->fixed_recharge_fee;

        if ($wallet->balance < $request->amount) {
            return response()->json([
                'success' => false,
                'error' => "no_enough_balance",
                'message' => __('no enough balance')
            ], 400);
        }

        $data = [
            'amount' => $request->amount,        // total deducted from wallet
            'fees' => $fees,                     // total system fee
            'actualAmount' => $request->amount - $fees // net credited to card
        ];

        try {
            DB::transaction(function () use ($data, $wallet, $card) {
                // Recharge card through service
                $this->virtualCardService->recharge($card, $data['actualAmount']);

                // Create recharge transaction
                $transaction_id_response =  $this->virtualCardService->rechargeVirtualCardTransaction($data, $card->external_id);

                // Insert charge
                $this->virtualCardService->insertBuyCardCharge(
                    $this->api_settings->fixed_recharge_fee,
                    $this->api_settings->percent_recharge_fee,
                    $data['fees'],
                    auth()->user(),
                    $transaction_id_response,
                    $card->maskedNumber
                );

                // Deduct wallet balance
                $wallet->balance = $wallet->balance - $data['amount'];
                $wallet->save();
            });

            return response()->json([
                'success' => true,
                'message' => __("Card Has Been Recharged Successfully"),
            ]);
        } catch (\Throwable $th) {
            Log::error('Error recharging card: ' . $th->getMessage(), [
                'exception' => $th
            ]);

            return response()->json([
                'success' => false,
                'error' => "unavailable",
                'message' => __('service is not available now, please try later')
            ]);
        }
    }

    public function freeze(string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)
            ->where('user_id', auth()->id())
            ->where('is_terminated', 0)
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        $validator = Validator::make(request()->all(), [
            'action' => 'required|string|in:freeze,unfreeze',
        ]);

        if ($validator->fails()) {
            return [
                'success' => false,
                'errors' => $validator->errors()
            ];
        }

        $action = request('action');

        if ($action == 'freeze' && $card->is_default) {
            return response()->json([
                'success' => false,
                'message' => __("Can't freeze default card"),
            ]);
        }

        if (($card->is_frozen && $action == 'freeze')) {
            return response()->json([
                'success' => false,
                'message' => __("Card Already Frozen"),
            ]);
        }

        if (!$card->is_frozen && $action == 'unfreeze') {
            return response()->json([
                'success' => false,
                'message' => __("Card Already Unfrozen"),
            ]);
        }

        $this->virtualCardService->freeze($card, $action);

        $message = request('action') == 'freeze' ? __("Card Has Been Frozen Successfully") : __("Card Has Been Unfrozen Successfully");

        return response()->json([
            'success' => true,
            'message' => __($message),
            'data' => null
        ]);
    }

    public function transactions(string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)->where('user_id', auth()->id())->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        $transactions = $this->virtualCardService->getCardTransactions($card);

        return response()->json([
            'success' => true,
            'data' => $transactions
        ]);
    }

    public function terminate(string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)
            ->where('user_id', auth()->id())
            ->where('is_terminated', 0)
            ->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        if ($card->is_terminated == 1) {
            return response()->json([
                'success' => false,
                'message' => __("Card Already Terminated"),
            ]);
        }

        $this->virtualCardService->terminate($card);

        return response()->json([
            'success' => true,
            'message' => __("Card Has Been Terminated Successfully"),
            'data' => null
        ]);
    }

    public function makeDefault(string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)->where('user_id', auth()->id())->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        if ($card->status == VirtualUserCard::$Inactive || $card->is_terminated == 1 || $card->is_fronzen == 1) {
            return response()->json([
                'success' => false,
                'message' => __("Can't set this card as default"),
            ]);
        }

        $card->is_default = 1;
        $card->save();

        $otherCards = VirtualUserCard::where('user_id', auth()->id())->where('id', '!=', $card->id)->get();

        foreach ($otherCards as $card) {
            $card->is_default = 0;
            $card->save();
        }

        return response()->json([
            'success' => true,
            'message' => __("Card Has Been Set As Default Successfully"),
        ]);
    }

    public function getDefault()
    {
        $card = VirtualUserCard::where('user_id', auth()->id())->where('is_default', 1)->first();

        return response()->json([
            'success' => true,
            'data' => $card ? CardResource::make($card) : null
        ]);
    }

    public function getCardNumber(string $cardId)
    {
        $card = VirtualUserCard::where('cardId', $cardId)->where('user_id', auth()->id())->first();

        if (!$card) {
            return response()->json([
                'success' => false,
                'message' => __("Card Not Found"),
            ]);
        }

        $cardDetailsfromLirat = $this->virtualCardService->getCardDetails($card);

        return response()->json([
            'success' => true,
            'data' => [
                'cardNumber' => $cardDetailsfromLirat['cardNumber'],
                'cvv' => $cardDetailsfromLirat['cvv'],
                'balance' => (string) $cardDetailsfromLirat['balance'],
            ]
        ]);
    }
}
