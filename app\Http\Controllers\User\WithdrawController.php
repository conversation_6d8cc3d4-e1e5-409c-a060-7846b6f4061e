<?php

namespace App\Http\Controllers\User;

use Exception;
use App\Models\Network;
use App\Models\UserWallet;
use App\Models\Transaction;
use Jenssegers\Agent\Agent;
use Illuminate\Http\Request;
use App\Models\Admin\Currency;
use App\Models\UserNotification;
use Illuminate\Support\Facades\DB;
use App\Models\Admin\BasicSettings;
use Illuminate\Support\Facades\Log;
use App\Constants\NotificationConst;
use App\Http\Controllers\Controller;
use App\Models\Admin\PaymentGateway;
use Illuminate\Support\Facades\Auth;
use App\Constants\PaymentGatewayConst;
use App\Http\Helpers\Api\Helpers;
use App\Traits\ControlDynamicInputFields;
use App\Models\Admin\PaymentGatewayCurrency;
use Illuminate\Support\Facades\Validator;

class WithdrawController extends Controller
{
    use ControlDynamicInputFields;

    public function index()
    {
        $page_title = __("Withdraw Money");
        $networks = Network::all();
        $PaymentGatewayCurrency = PaymentGateway::find(2)->currencies()->first();
        $transactions = Transaction::auth()->withdrawMoney()->orderByDesc("id")->latest()->take(10)->get();
        $user_wallet = auth()->user()->wallets->first();

        return view('user.sections.withdraw.index', compact('page_title', 'transactions', 'networks', 'PaymentGatewayCurrency', 'user_wallet'));
    }

    public function paymentInsert(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'network'          => 'required|string|max:255',
            'network_address'  => 'required|string|max:255',
            'amount'           => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            $error = ['errors' => $validator->errors()->all()];
            return Helpers::validation($error);
        }

        $user = auth()->user();
        $userWallet = $user->wallets->first();

        if (!$userWallet) {
            return response()->json(['success' => false, 'message' => __('User wallet not found')], 400);
        }

        $gate = PaymentGatewayCurrency::whereHas('gateway', function ($gateway) {
            $gateway->where('id', 2);
        })->first();

        if (!$gate) {
            return response()->json(['success' => false, 'message' => __("Invalid Gateway!")], 400);
        }

        // Additional validations and data processing
        $amount = $request->amount;
        $min_limit =  $gate->min_limit / $gate->rate;
        $max_limit =  $gate->max_limit / $gate->rate;

        if ($amount < $min_limit) {
            return response()->json(['success' => false, 'message' => __('The amount is less than the minimum')], 400);
        } else if ($amount > $max_limit) {
            return response()->json(['success' => false, 'message' => __('The amount exceeds the maximum')], 400);
        }

        // Charges calculation
        $percent_charge   = ($amount * $gate->percent_charge) / 100;
        $fixedCharge      = $gate->fixed_charge;
        $charge           = $percent_charge + $fixedCharge;

        if ($amount <= $charge) {
            return response()->json(['success' => false, 'message' => __('The amount is less than the charge')], 400);
        }

        $conversion_amount = $amount / $gate->rate;
        $baseFixedCharge   = $fixedCharge * $gate->rate;
        $basePercentCharge = $percent_charge * $gate->rate;
        $base_total_charge = $baseFixedCharge + $basePercentCharge;

        // ✅ Deduct exactly what user typed
        $reduceAbleTotal = $amount;

        // ✅ What the user actually gets
        $will_get = $amount - $charge;

        if ($reduceAbleTotal > $userWallet->balance) {
            return response()->json(['success' => false, 'message' => __('Sorry, insufficient balance')], 400);
        }

        try {

            // Store data in session
            $data = [
                'user_id'              => $user->id,
                'gateway_name'         => $gate->gateway->name,
                'gateway_type'         => $gate->gateway->type,
                'wallet_id'            => $userWallet->id,
                'trx_id'               => 'WM' . getTrxNum(),
                'amount'               => $amount,           // deducted from wallet
                'base_cur_fixed_charge' => $baseFixedCharge,
                'base_cur_percent_charge' => $basePercentCharge,
                'base_cur_charge'      => $base_total_charge,
                'gateway_id'           => $gate->gateway->id,
                'gateway_currency_id'  => $gate->id,
                'gateway_currency'     => strtoupper($gate->currency_code),
                'gateway_percent_charge' => $percent_charge,
                'gateway_fixed_charge' => $fixedCharge,
                'gateway_charge'       => $charge,
                'gateway_rate'         => $gate->rate,
                'conversion_amount'    => $conversion_amount,
                'will_get'             => $will_get,   // ✅ what user gets
                'payable'              => $will_get,   // ✅ payable = what user gets
                'network'              => $request->network,
                'network_address'      => $request->network_address,
            ];

            $gateway = PaymentGateway::find($data['gateway_id']);
            $payment_fields = $gateway->input_fields ?? [];
            $validation_rules = $this->generateValidationRules($payment_fields);
            $validatedData = $request->validate($validation_rules);
            $get_values = $this->placeValueWithFields($payment_fields, $validatedData);
            $inserted_id = $this->insertRecordManual((object) $data, $gateway, $get_values);
            $this->insertChargesManual((object) $data, $inserted_id);
            $this->insertDeviceManual((object) $data, $inserted_id);
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Payment Insert Error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => __('An error occurred. Please try again.')], 500);
        }
    }

    public function confirmMoneyOut(Request $request)
    {
        $moneyOutData = session()->get('moneyoutData');
        if (!$moneyOutData) {
            logger()->warning('No payment data found in session');
            return back()->with(['error' => [__('No payment data found')]]);
        }

        $gateway = PaymentGateway::find($moneyOutData['gateway_id']);
        $payment_fields = $gateway->input_fields ?? [];

        $validation_rules = $this->generateValidationRules($payment_fields);
        $validatedData = $request->validate($validation_rules);

        $get_values = $this->placeValueWithFields($payment_fields, $validatedData);

        try {
            $inserted_id = $this->insertRecordManual((object) $moneyOutData, $gateway, $get_values);
            $this->insertChargesManual((object) $moneyOutData, $inserted_id);
            $this->insertDeviceManual((object) $moneyOutData, $inserted_id);

            session()->forget('moneyoutData');

            return redirect()->route("user.withdraw.index")->with(['success' => [__('Withdraw Money Request Sent To Admin Successfully')]]);
        } catch (Exception $e) {
            return back()->with(['error' => [__("Something Went Wrong! Please Try Again")]]);
        }
    }

    public function preview()
    {
        $moneyOutData = (object)session()->get('moneyoutData');
        $moneyOutDataExist = session()->get('moneyoutData');
        if ($moneyOutDataExist  == null) {
            return redirect()->route('user.withdraw.index');
        }
        $gateway = PaymentGateway::where('id', $moneyOutData->gateway_id)->first();
        if ($gateway->type == "AUTOMATIC") {
            $page_title = __("Withdraw Via") . " " . $gateway->name;
            return back()->with(['error' => [__("Something Went Wrong! Please Try Again")]]);
        } else {
            $page_title = __("Withdraw Via") . " " . $gateway->name;
            return view('user.sections.withdraw.preview', compact('page_title', 'gateway', 'moneyOutData'));
        }
    }

    public function insertRecordManual($moneyOutData, $gateway, $get_values)
    {
        if ($moneyOutData->gateway_type == "AUTOMATIC") {
            $status = 1;
        } else {
            $status = 2;
        }

        $details = [
            'user_data' => $get_values,
            'withdraw_data' => $moneyOutData,
        ];

        $trx_id = $moneyOutData->trx_id ?? 'MO' . getTrxNum();
        $authWallet = UserWallet::where('id', $moneyOutData->wallet_id)->where('user_id', $moneyOutData->user_id)->first();
        $afterCharge = $authWallet->balance - $moneyOutData->amount;

        DB::beginTransaction();

        try {
            $id = DB::table("transactions")->insertGetId([
                'user_id'                     => auth()->id(),
                'user_wallet_id'              => $moneyOutData->wallet_id,
                'payment_gateway_currency_id' => $moneyOutData->gateway_currency_id,
                'type'                        => PaymentGatewayConst::WITHDRAWMONEY,
                'trx_id'                      => $trx_id,
                'request_amount'              => $moneyOutData->amount,     // ✅ what user entered
                'payable'                     => $moneyOutData->will_get,   // ✅ what user actually gets
                'available_balance'           => $afterCharge,
                'remark'                      => ucwords(remove_speacial_char(PaymentGatewayConst::WITHDRAWMONEY, " ")) . " by " . $gateway->name,
                'details'                     => json_encode($details),
                'status'                      => $status,
                'created_at'                  => now(),
            ]);

            $this->updateWalletBalanceManual($authWallet, $afterCharge);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception(__("Something Went Wrong! Please Try Again"));
        }
        return $id;
    }

    public function updateWalletBalanceManual($authWalle, $afterCharge)
    {
        $authWalle->update([
            'balance'   => $afterCharge,
        ]);
    }

    public function insertChargesManual($moneyOutData, $id)
    {
        if (Auth::guard(get_auth_guard())->check()) {
            $user = auth()->guard(get_auth_guard())->user();
        }
        DB::beginTransaction();
        try {
            DB::table('transaction_charges')->insert([
                'transaction_id'    => $id,
                'percent_charge'    => $moneyOutData->base_cur_percent_charge,
                'fixed_charge'      => $moneyOutData->base_cur_fixed_charge,
                'total_charge'      => $moneyOutData->base_cur_charge,
                'created_at'        => now(),
            ]);
            DB::commit();

            //notification
            $notification_content = [
                'title'         => "Withdraw Money",
                'message'       => "Your Withdraw Request Send To Admin" . " " . $moneyOutData->amount . ' ' . get_default_currency_code() . " " . "Successful",
                'image'         => get_image($user->image, 'user-profile'),
            ];

            UserNotification::create([
                'type'      => NotificationConst::WITHDRAWMONEY,
                'user_id'  =>  auth()->user()->id,
                'message'   => $notification_content,
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception(__("Something Went Wrong! Please Try Again"));
        }
    }

    public function insertDeviceManual($output, $id)
    {
        $client_ip = request()->ip() ?? false;
        $location = geoip()->getLocation($client_ip);
        $agent = new Agent();

        // $mac = exec('getmac');
        // $mac = explode(" ",$mac);
        // $mac = array_shift($mac);
        $mac = "";

        DB::beginTransaction();
        try {
            DB::table("transaction_devices")->insert([
                'transaction_id' => $id,
                'ip'            => $client_ip,
                'mac'           => $mac,
                'city'          => $location['city'] ?? "",
                'country'       => $location['country'] ?? "",
                'longitude'     => $location['lon'] ?? "",
                'latitude'      => $location['lat'] ?? "",
                'timezone'      => $location['timezone'] ?? "",
                'browser'       => $agent->browser() ?? "",
                'os'            => $agent->platform() ?? "",
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception(__("Something Went Wrong! Please Try Again"));
        }
    }
}
