<?php

namespace App\Http\Controllers\Api\User;

use Exception;
use App\Models\Network;
use App\Models\UserWallet;
use App\Models\Transaction;
use Jenssegers\Agent\Agent;
use Illuminate\Http\Request;
use App\Models\TemporaryData;
use App\Models\Admin\Currency;
use App\Models\UserNotification;
use App\Http\Helpers\Api\Helpers;
use Illuminate\Support\Facades\DB;
use App\Models\Admin\BasicSettings;
use App\Constants\NotificationConst;
use App\Http\Controllers\Controller;
use App\Models\Admin\PaymentGateway;
use Illuminate\Support\Facades\Auth;
use App\Constants\PaymentGatewayConst;
use App\Http\Resources\NetworkResource;
use App\Traits\ControlDynamicInputFields;
use Illuminate\Support\Facades\Validator;
use App\Models\Admin\PaymentGatewayCurrency;
use Illuminate\Support\Facades\Log;

class WithdrawController extends Controller
{
    use ControlDynamicInputFields;

    public function withdrawInfo()
    {
        $user = auth()->user();
        $userWallet = UserWallet::where('user_id', $user->id)->get()->map(function ($data) {
            return [
                'balance' => (string) getAmount($data->balance, 2),
                'currency' => get_default_currency_code(),
            ];
        })->first();

        $networks = Network::all();
        $payment_gateway = PaymentGatewayCurrency::where('payment_gateway_id', 2)->first();

        $data = [
            'userWallet'   =>   (object)$userWallet,
            'charges' => [
                'min_limit' => (string) getAmount($payment_gateway->min_limit, 2),
                'max_limit' => (string) getAmount($payment_gateway->max_limit, 2),
                'fixed_fees' => (string) getAmount($payment_gateway->fixed_charge, 2),
                'percent_fees' => (string) getAmount($payment_gateway->percent_charge, 2)
            ],
            'networks' => NetworkResource::collection($networks)
        ];

        $message =  ['success' => [__("Withdraw Information!")]];

        return Helpers::success($data, $message);
    }

    public function withdrawInsert(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'network'          => 'required|string|max:255',
            'network_address'  => 'required|string|max:255',
            'amount'           => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            $error = ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }

        $user = auth()->user();
        $userWallet = $user->wallets->first();

        if (!$userWallet) {
            return response()->json(['success' => false, 'message' => __('User wallet not found')], 400);
        }

        $gate = PaymentGatewayCurrency::whereHas('gateway', function ($gateway) {
            $gateway->where('id', 2);
        })->first();

        if (!$gate) {
            return response()->json(['success' => false, 'message' => __("Invalid Gateway!")], 400);
        }

        // Additional validations and data processing
        $amount = $request->amount;
        $min_limit =  $gate->min_limit / $gate->rate;
        $max_limit =  $gate->max_limit / $gate->rate;

        if ($amount < $min_limit) {
            return response()->json(['success' => false, 'message' => __('The amount is less than the minimum')], 400);
        } else if ($amount > $max_limit) {
            return response()->json(['success' => false, 'message' => __('The amount exceeds the maximum')], 400);
        }

        // Charges calculation
        $percent_charge   = ($amount * $gate->percent_charge) / 100;
        $fixedCharge      = $gate->fixed_charge;
        $charge           = $percent_charge + $fixedCharge;

        if ($amount <= $charge) {
            return response()->json(['success' => false, 'message' => __('The amount is less than the charge')], 400);
        }

        $conversion_amount = $amount / $gate->rate;
        $baseFixedCharge   = $fixedCharge * $gate->rate;
        $basePercentCharge = $percent_charge * $gate->rate;
        $base_total_charge = $baseFixedCharge + $basePercentCharge;

        // ✅ Deduct exactly what user typed
        $reduceAbleTotal = $amount;

        // ✅ What the user actually gets
        $will_get = $amount - $charge;

        if ($reduceAbleTotal > $userWallet->balance) {
            return response()->json(['success' => false, 'message' => __('Sorry, insufficient balance')], 400);
        }

        try {

            // Store data in session
            $data = [
                'user_id'              => $user->id,
                'gateway_name'         => $gate->gateway->name,
                'gateway_type'         => $gate->gateway->type,
                'wallet_id'            => $userWallet->id,
                'trx_id'               => 'WM' . getTrxNum(),
                'amount'               => $amount,           // deducted from wallet
                'base_cur_fixed_charge' => $baseFixedCharge,
                'base_cur_percent_charge' => $basePercentCharge,
                'base_cur_charge'      => $base_total_charge,
                'gateway_id'           => $gate->gateway->id,
                'gateway_currency_id'  => $gate->id,
                'gateway_currency'     => strtoupper($gate->currency_code),
                'gateway_percent_charge' => $percent_charge,
                'gateway_fixed_charge' => $fixedCharge,
                'gateway_charge'       => $charge,
                'gateway_rate'         => $gate->rate,
                'conversion_amount'    => $conversion_amount,
                'will_get'             => $will_get,   // ✅ what user gets
                'payable'              => $will_get,   // ✅ payable = what user gets
                'network'              => $request->network,
                'network_address'      => $request->network_address,
            ];

            $gateway = PaymentGateway::find($data['gateway_id']);
            $payment_fields = $gateway->input_fields ?? [];
            $validation_rules = $this->generateValidationRules($payment_fields);
            $validatedData = $request->validate($validation_rules);
            $get_values = $this->placeValueWithFields($payment_fields, $validatedData);
            $inserted_id = $this->insertRecordManual((object) $data, $gateway, $get_values);
            $this->insertChargesManual((object) $data, $inserted_id);
            $this->insertDeviceManual((object) $data, $inserted_id);
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Payment Insert Error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => __('An error occurred. Please try again.')], 500);
        }
    }

    public function withdrawConfirmed(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'trx'  => "required",
        ]);

        if ($validator->fails()) {
            $error =  ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }

        $track = TemporaryData::where('identifier', $request->trx)->where('type', PaymentGatewayConst::WITHDRAWMONEY)->first();
        if (!$track) {
            $error = ['error' => [__("Sorry, your payment information is invalid")]];
            return Helpers::error($error);
        }

        $moneyOutData =  $track->data;
        $gateway = PaymentGateway::where('id', $moneyOutData->gateway_id)->first();

        if ($gateway->type != "MANUAL") {
            $error = ['error' => [__("Invalid request, it is not manual gateway request")]];
            return Helpers::error($error);
        }

        $payment_fields = $gateway->input_fields ?? [];
        $validation_rules = $this->generateValidationRules($payment_fields);
        $validator2 = Validator::make($request->all(), $validation_rules);

        if ($validator2->fails()) {
            $message =  ['error' => $validator2->errors()->all()];
            return Helpers::error($message);
        }

        $validated = $validator2->validate();
        $get_values = $this->placeValueWithFields($payment_fields, $validated);

        try {
            //send notifications
            $user = auth()->user();
            $inserted_id = $this->insertRecordManual($moneyOutData, $gateway, $get_values);
            $this->insertChargesManual($moneyOutData, $inserted_id);
            $this->insertDeviceManual($moneyOutData, $inserted_id);
            $track->delete();
            $message =  ['success' => [__("Withdraw Money Request Send To Admin Successful")]];
            return Helpers::onlysuccess($message);
        } catch (Exception $e) {
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }

    public function insertRecordManual($moneyOutData, $gateway, $get_values)
    {
        if ($moneyOutData->gateway_type == "AUTOMATIC") {
            $status = 1;
        } else {
            $status = 2;
        }

        $details = [
            'user_data' => $get_values,
            'withdraw_data' => $moneyOutData,
        ];

        $trx_id = $moneyOutData->trx_id ?? 'MO' . getTrxNum();
        $authWallet = UserWallet::where('id', $moneyOutData->wallet_id)->where('user_id', $moneyOutData->user_id)->first();
        $afterCharge = $authWallet->balance - $moneyOutData->amount;

        DB::beginTransaction();

        try {
            $id = DB::table("transactions")->insertGetId([
                'user_id'                     => auth()->id(),
                'user_wallet_id'              => $moneyOutData->wallet_id,
                'payment_gateway_currency_id' => $moneyOutData->gateway_currency_id,
                'type'                        => PaymentGatewayConst::WITHDRAWMONEY,
                'trx_id'                      => $trx_id,
                'request_amount'              => $moneyOutData->amount,     // ✅ what user entered
                'payable'                     => $moneyOutData->will_get,   // ✅ what user actually gets
                'available_balance'           => $afterCharge,
                'remark'                      => ucwords(remove_speacial_char(PaymentGatewayConst::WITHDRAWMONEY, " ")) . " by " . $gateway->name,
                'details'                     => json_encode($details),
                'status'                      => $status,
                'created_at'                  => now(),
            ]);

            $this->updateWalletBalanceManual($authWallet, $afterCharge);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception(__("Something Went Wrong! Please Try Again"));
        }
        return $id;
    }

    public function updateWalletBalanceManual($authWalle, $afterCharge)
    {
        $authWalle->update([
            'balance'   => $afterCharge,
        ]);
    }

    public function insertChargesManual($moneyOutData, $id)
    {

        if (Auth::guard(get_auth_guard())->check()) {
            $user = auth()->guard(get_auth_guard())->user();
        }

        DB::beginTransaction();

        try {
            DB::table('transaction_charges')->insert([
                'transaction_id'    => $id,
                'percent_charge'    => $moneyOutData->base_cur_percent_charge,
                'fixed_charge'      => $moneyOutData->base_cur_fixed_charge,
                'total_charge'      => $moneyOutData->base_cur_charge,
                'created_at'        => now(),
            ]);
            DB::commit();

            //notification
            $notification_content = [
                'title'         => "Withdraw Money",
                'message'       => "Your Withdraw Request Send To Admin" . " " . $moneyOutData->amount . ' ' . get_default_currency_code() . " " . "Successful",
                'image'         => get_image($user->image, 'user-profile'),
            ];

            UserNotification::create([
                'type'      => NotificationConst::WITHDRAWMONEY,
                'user_id'  =>  auth()->user()->id,
                'message'   => $notification_content,
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }

    public function insertDeviceManual($output, $id)
    {
        $client_ip = request()->ip() ?? false;
        $location = geoip()->getLocation($client_ip);
        $agent = new Agent();

        // $mac = exec('getmac');
        // $mac = explode(" ",$mac);
        // $mac = array_shift($mac);
        $mac = "";

        DB::beginTransaction();
        try {
            DB::table("transaction_devices")->insert([
                'transaction_id' => $id,
                'ip'            => $client_ip,
                'mac'           => $mac,
                'city'          => $location['city'] ?? "",
                'country'       => $location['country'] ?? "",
                'longitude'     => $location['lon'] ?? "",
                'latitude'      => $location['lat'] ?? "",
                'timezone'      => $location['timezone'] ?? "",
                'browser'       => $agent->browser() ?? "",
                'os'            => $agent->platform() ?? "",
            ]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            $error = ['error' => [__("Something Went Wrong! Please Try Again")]];
            return Helpers::error($error);
        }
    }

    public function withdraw(Request $request)
    {
        // Step 1: Validate the request
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|gt:0',
            'gateway' => 'required'
        ]);
        if ($validator->fails()) {
            $error =  ['error' => $validator->errors()->all()];
            return Helpers::validation($error);
        }

        // Step 2: Check KYC verification
        $basic_setting = BasicSettings::first();
        $user = auth()->user();


        // Step 3: Check user wallet
        $userWallet = UserWallet::where('user_id', $user->id)->where('status', 1)->first();
        if (!$userWallet) {
            $error = ['error' => [__('User wallet not found')]];
            return Helpers::error($error);
        }

        // Step 4: Validate gateway
        $gate = PaymentGatewayCurrency::whereHas('gateway', function ($gateway) {
            $gateway->where('slug', PaymentGatewayConst::money_out_slug());
            $gateway->where('status', 1);
        })->where('payment_gateway_id', $request->gateway)->first();
        if (!$gate) {
            $error = ['error' => [__("Invalid Gateway!")]];
            return Helpers::error($error);
        }

        // Step 5: Validate base currency
        $baseCurrency = Currency::default();
        if (!$baseCurrency) {
            $error = ['error' => [__('Default Currency Not Setup Yet')]];
            return Helpers::error($error);
        }

        // Step 6: Validate amount limits
        $amount = $request->amount;
        $min_limit = $gate->min_limit / $gate->rate;
        $max_limit = $gate->max_limit / $gate->rate;
        if ($amount < $min_limit || $amount > $max_limit) {
            $error = ['error' => [__('Please follow the transaction limit')]];
            return Helpers::error($error);
        }

        // Step 7: Calculate charges and amounts
        $fixedCharge = $gate->fixed_charge;
        $percent_charge = (($request->amount / 100) * $gate->percent_charge);
        $charge = $fixedCharge + $percent_charge;
        $conversion_amount = $request->amount * $gate->rate;
        $will_get = $conversion_amount;

        $baseFixedCharge = $gate->fixed_charge / $gate->rate;
        $basePercent_charge = (($request->amount / 100) * $gate->percent_charge) / $gate->rate;
        $base_total_charge = $baseFixedCharge + $basePercent_charge;
        $reduceAbleTotal = $amount + $base_total_charge;

        if ($reduceAbleTotal > $userWallet->balance) {
            $error = ['error' => [__('Sorry, insufficient balance')]];
            return Helpers::error($error);
        }

        // Step 8: Insert transaction data
        $insertData = [
            'user_id' => $user->id,
            'gateway_name' => strtolower($gate->gateway->name),
            'gateway_type' => $gate->gateway->type,
            'wallet_id' => $userWallet->id,
            'trx_id' => 'WM' . getTrxNum(),
            'amount' => $amount,
            'base_cur_fixed_charge' => $baseFixedCharge,
            'base_cur_percent_charge' => $basePercent_charge,
            'base_cur_charge' => $base_total_charge,
            'base_cur_rate' => $baseCurrency->rate,
            'gateway_id' => $gate->gateway->id,
            'gateway_currency_id' => $gate->id,
            'gateway_currency' => strtoupper($gate->currency_code),
            'gateway_percent_charge' => $percent_charge,
            'gateway_fixed_charge' => $fixedCharge,
            'gateway_charge' => $charge,
            'gateway_rate' => $gate->rate,
            'conversion_amount' => $conversion_amount,
            'will_get' => $will_get,
            'payable' => $reduceAbleTotal,
        ];



        // Step 9: Handle manual and automatic gateways
        $payment_gateway = PaymentGateway::where('id', $gate->payment_gateway_id)->first();
        $payment_informations = [
            'trx' => $insertData['trx_id'],
            'gateway_currency_name' => $gate->name,
            'request_amount' => getAmount($request->amount, 2) . ' ' . get_default_currency_code(),
            'exchange_rate' => "1" . ' ' . get_default_currency_code() . ' = ' . getAmount($gate->rate) . ' ' . $gate->currency_code,
            'conversion_amount' => getAmount($conversion_amount, 2) . ' ' . $gate->currency_code,
            'total_charge' => getAmount($base_total_charge, 2) . ' ' . get_default_currency_code(),
            'will_get' => getAmount($will_get, 2) . ' ' . $gate->currency_code,
            'payable' => getAmount($reduceAbleTotal, 2) . ' ' . get_default_currency_code(),
        ];
        $details = [
            'withdraw_data' => $insertData,
        ];
        if ($payment_gateway->type == "AUTOMATIC") {
            $status = 1;
        } else {
            $status = 2;
        }
        $transactionData = [
            'user_id' => auth()->user()->id,
            'user_wallet_id' => $userWallet->id,
            'payment_gateway_currency_id' => $gate->id,
            'type' => PaymentGatewayConst::WITHDRAWMONEY,
            'trx_id' => $insertData['trx_id'],
            'request_amount' => $request->amount,
            'payable' => $reduceAbleTotal,
            'available_balance' => $will_get,
            'remark' => ucwords(remove_speacial_char(PaymentGatewayConst::WITHDRAWMONEY, " ")) . " by " . $payment_gateway->name,
            // 'details' => json_encode($details),
            'status' => $status,
            'created_at' => now(),
        ];


        if ($gate->gateway->type == "AUTOMATIC") {
            $url = route('api.withdraw.automatic.confirmed');
            $data = [
                'payment_informations' => $payment_informations,
                'gateway_type' => $payment_gateway->type,
                'gateway_currency_name' => $gate->name,
                'alias' => $gate->alias,
                'url' => $url ?? '',
                'method' => "post",
            ];
            $transaction = $this->insertTransaction($transactionData);

            $message = ['success' => ['Withdraw Money Inserted Successfully']];
            return Helpers::success($data, $message);
        } else {
            $url = route('api.withdraw.manual.confirmed');
            $data = [
                'payment_informations' => $payment_informations,
                'gateway_type' => $payment_gateway->type,
                'gateway_currency_name' => $gate->name,
            ];
            $transaction = $this->insertTransaction($transactionData);

            $message = ['success' => [__('Withdraw Money Inserted Successfully')]];
            return Helpers::success($data, $message);
        }
    }

    public function insertTransaction($transactionData)
    {
        $transaction = new Transaction();
        $transaction->user_id = $transactionData['user_id'];
        $transaction->user_wallet_id = $transactionData['user_wallet_id'];
        $transaction->payment_gateway_currency_id = $transactionData['payment_gateway_currency_id'];
        $transaction->type = $transactionData['type'];
        $transaction->trx_id = $transactionData['trx_id'];
        $transaction->request_amount = $transactionData['request_amount'];
        $transaction->payable = $transactionData['payable'];
        $transaction->available_balance = $transactionData['available_balance'];
        $transaction->remark = $transactionData['remark'];
        // $transaction->details = $transactionData['details'];
        $transaction->status = $transactionData['status'];
        $transaction->created_at = $transactionData['created_at'];
        $transaction->save();

        return $transaction;
    }
    public function Networks()
    {
        $network = Network::all();
        $message = ['success' => [__('Success')]];

        return Helpers::success($network, $message);
    }
}
