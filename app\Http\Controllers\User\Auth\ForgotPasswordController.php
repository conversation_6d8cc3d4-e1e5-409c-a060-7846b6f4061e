<?php

namespace App\Http\Controllers\User\Auth;

use App\Constants\GlobalConst;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserPasswordReset;
use App\Notifications\User\Auth\PasswordResetEmail;
use App\Providers\Admin\BasicSettingsProvider;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rules\Password;
use Illuminate\Support\Facades\RateLimiter;


class ForgotPasswordController extends Controller
{
    public function showForgotForm()
    {
        $page_title = __("Forgot Password");
        return view('user.auth.forgot-password.forgot', compact('page_title'));
    }

    public function sendCode(Request $request)
    {
        $request->validate([
            'user' => 'required|email|max:100',
        ]);

        $column = filter_var($request->user, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        $user = User::where($column, $request->user)->first();

        if (!$user) {
            throw ValidationException::withMessages(['user' => __("User doesn't exist")]);
        }

        if ($user->social_type) {
            throw ValidationException::withMessages(['user' => __("You are logged in via {$user->social_type}. Please use that login method.")]);
        }

        $token = generate_unique_string("user_password_resets", "token", 80);
        $code = generate_random_code();

        try {
            UserPasswordReset::where("user_id", $user->id)->delete();
            $password_reset = UserPasswordReset::create([
                'user_id' => $user->id,
                'token' => $token,
                'code' => $code,
            ]);
            $user->notify(new PasswordResetEmail($user, $password_reset));
        } catch (Exception $e) {
            return back()->with(['error' => [__('Something went wrong! Please try again')]]);
        }

        return redirect()->route('user.password.verify.form', $token)->with(['success' => [__("Verification code sent successfully")]]);
    }

    public function showVerifyForm($token)
    {
        $page_title = __("Verify User");
        $password_reset = UserPasswordReset::where("token", $token)->firstOrFail();

        $resend_time = Carbon::now()->diffInSeconds($password_reset->created_at->addMinutes(GlobalConst::USER_PASS_RESEND_TIME_MINUTE), false);
        $user_email = $password_reset->user->email ?? "";

        return view('user.auth.forgot-password.verify', compact('page_title', 'token', 'user_email', 'resend_time'));
    }

    public function verifyCode(Request $request, $token)
    {
        $request->merge(['token' => $token]);

        // Rate limiting: Max 5 attempts per day (24 hours)
        $key = 'verify-attempts:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            throw ValidationException::withMessages([
                'code' => __('Too many attempts. Please try again later.', [
                    'hours' => ceil(RateLimiter::availableIn($key) / 3600),
                ]),
            ]);
        }

        // Validate request
        $validated = Validator::make($request->all(), [
            'token' => "required|string|exists:user_password_resets,token",
            'code' => "required|array",
            'code.*' => "required|numeric",
        ])->validate();

        $code = implode("", $request->code);
        $password_reset = UserPasswordReset::where("token", $token)->first();

        if (!$password_reset || $password_reset->code != $code) {
            RateLimiter::hit($key, 86400); // Store failed attempt for 24 hours (86400 seconds)
            throw ValidationException::withMessages([
                'code' => __("Verification OTP is Invalid"),
            ]);
        }

        RateLimiter::clear($key); // Reset failed attempts on success
        return redirect()->route('user.password.forgot.reset.form', $token);
    }

    public function resendCode($token)
    {
        $password_reset = UserPasswordReset::where('token', $token)->firstOrFail();

        if (Carbon::now()->lessThanOrEqualTo($password_reset->created_at->addMinutes(GlobalConst::USER_PASS_RESEND_TIME_MINUTE))) {
            throw ValidationException::withMessages(['code' => __('Please wait before requesting a new code.')]);
        }

        try {
            $password_reset->update(['code' => generate_random_code(), 'created_at' => now()]);
            $password_reset->user->notify(new PasswordResetEmail($password_reset->user, (object) $password_reset->toArray()));
        } catch (Exception $e) {
            return back()->with(['error' => [__('Something went wrong! Please try again')]]);
        }

        return redirect()->route('user.password.verify.form', $token)->with(['success' => [__('Verification code resent successfully.')]]);
    }

    public function showResetForm($token)
    {
        $page_title = __("Reset Password");
        return view('user.auth.forgot-password.reset', compact('page_title', 'token'));
    }

    public function resetPassword(Request $request, $token)
    {
        $request->validate([
            'password' => [
                'required',
                Password::min(8)->letters()->mixedCase()->numbers()->symbols()->uncompromised(),
                'confirmed',
            ],
        ]);

        $password_reset = UserPasswordReset::where("token", $token)->firstOrFail();

        try {
            $password_reset->user->update(['password' => Hash::make($request->password)]);
            $password_reset->delete();
        } catch (Exception $e) {
            return back()->with(['error' => [__('Something went wrong! Please try again')]]);
        }

        return redirect()->route('index')->with(['success' => [__('Password reset successful. Please log in with your new password.')]]);
    }
}
